import 'package:flutter/material.dart';

class StepProgressIndicator extends StatelessWidget {
  final int stepCount;
  final int currentStep;
  final Color activeColor;
  final Color inactiveColor;

  const StepProgressIndicator({
    super.key,
    required this.stepCount,
    required this.currentStep,
    this.activeColor = const Color(0xFF6C5CE7),
    this.inactiveColor = const Color(0xFFE0E0E0),
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(stepCount, (index) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          margin: const EdgeInsets.symmetric(horizontal: 4.0),
          width: index == currentStep ? 24.0 : 8.0,
          height: 8.0,
          decoration: BoxDecoration(
            color: index == currentStep ? activeColor : inactiveColor,
            borderRadius: BorderRadius.circular(12),
          ),
        );
      }),
    );
  }
}
