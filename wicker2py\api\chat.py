import json
from flask import jsonify, Blueprint
from flask_jwt_extended import get_jwt_identity, jwt_required
from bson import ObjectId, json_util

chat_bp = Blueprint('chat_bp', __name__)

@chat_bp.route('/history/<room_id>', methods=['GET'])
@jwt_required()
def get_history(room_id):
    """Fetches the chat history for a given room (e.g., an order ID)."""
    db = chat_bp.db
    messages = list(db.messages.find({'room_id': room_id}).sort('timestamp', 1))
    return jsonify(json.loads(json_util.dumps(messages))), 200



# --- NEW: Endpoint to mark messages as read ---
@chat_bp.route('/<room_id>/mark-read', methods=['POST'])
@jwt_required()
def mark_messages_as_read(room_id):
    """Marks all messages in a room as read by the current user."""
    db = chat_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    # Add the current user's ID to the read_by array for all messages in the room
    # where they are not already present.
    db.messages.update_many(
        {'room_id': room_id, 'read_by': {'$ne': current_user_id}},
        {'$addToSet': {'read_by': current_user_id}}
    )
    return jsonify({"msg": "Messages marked as read"}), 200




















# import json
# from flask import jsonify, Blueprint
# from flask_jwt_extended import jwt_required, get_jwt_identity
# from bson import ObjectId, json_util
# import datetime
# from flask_socketio import emit, join_room, leave_room

# chat_bp = Blueprint('chat_bp', __name__)

# # This is a standard HTTP endpoint to fetch the message history for a room
# @chat_bp.route('/history/<room_id>', methods=['GET'])
# @jwt_required()
# def get_history(room_id):
#     """Fetches the chat history for a given room (e.g., an order ID)."""
#     db = chat_bp.db
    
#     # Optional: Add logic here to verify the current user is part of this chat room
    
#     messages = list(db.messages.find({'room_id': room_id}).sort('timestamp', 1))
#     return json.loads(json_util.dumps(messages)), 200

# # --- Real-Time WebSocket Event Handlers ---
# # These handlers will be registered with the socketio instance from app.py

# def register_socketio_handlers(socketio):
#     """Register SocketIO event handlers with the socketio instance."""

#     @socketio.on('join')
#     def on_join(data):
#         """User joins a room."""
#         room = data['room']
#         join_room(room)
#         print(f"User has entered room: {room}")

#     @socketio.on('leave')
#     def on_leave(data):
#         """User leaves a room."""
#         room = data['room']
#         leave_room(room)
#         print(f"User has left room: {room}")

#     @socketio.on('send_message')
#     def on_send_message(data):
#         """Receives a message from a client and broadcasts it to the room."""
#         db = chat_bp.db
#         room = data.get('room')
#         if not room:
#             return # Ignore messages without a room

#         # In the next phase, we will intercept this message and send it to the AI Chaperone

#         message_doc = {
#             "room_id": room,
#             "author_id": ObjectId(data.get('author_id')),
#             "author_username": data.get('author_username'),
#             "text": data.get('text'),
#             "timestamp": datetime.datetime.now(datetime.timezone.utc)
#         }

#         # Save the message to the database for history
#         db.messages.insert_one(message_doc)

#         # Broadcast the message to all clients in the room
#         emit('receive_message', json.loads(json_util.dumps(message_doc)), to=room)