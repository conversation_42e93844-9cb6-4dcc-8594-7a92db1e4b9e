from flask import jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

notifications_bp = Blueprint('notifications_bp', __name__)

@notifications_bp.route('/summary', methods=['GET'])
@jwt_required()
def get_notification_summary():
    """
    Checks all relevant collections for any items that require the user's attention
    and returns a summary.
    """
    db = notifications_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    # 1. Check for booking status updates the user hasn't seen
    unread_appointments = db.bookings.find_one({
        "client_id": current_user_id,
        "client_viewed": False
    })

    # 2. Check for unread chat messages
    # This requires finding all rooms the user is a part of (as buyer or seller)
    user_orders = list(db.orders.find(
        {"$or": [{"buyer_id": current_user_id}, {"seller_id": current_user_id}]},
        {"_id": 1} # Projection
    ))
    order_ids_as_strings = [str(order['_id']) for order in user_orders]

    unread_chats = db.messages.find_one({
        "room_id": {"$in": order_ids_as_strings},
        "read_by": {"$ne": current_user_id}
    })

    # 3. Check for pending booking requests for the user's businesses
    pending_bookings = db.bookings.find_one({
        "seller_id": current_user_id,
        "status": "pending_confirmation"
    })

    summary = {
        "has_unread_appointments": unread_appointments is not None,
        "has_unread_chats": unread_chats is not None,
        "has_pending_bookings": pending_bookings is not None
    }

    return jsonify(summary), 200