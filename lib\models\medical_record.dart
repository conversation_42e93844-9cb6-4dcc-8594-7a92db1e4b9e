class MedicalRecord {
  final String id;
  final String doctorId;
  final String patientId;
  final String recordCiphertext;

  MedicalRecord({
    required this.id,
    required this.doctorId,
    required this.patientId,
    required this.recordCiphertext,
  });

  factory MedicalRecord.fromJson(Map<String, dynamic> json) {
    return MedicalRecord(
      id: json['_id']['_oid'],
      doctorId: json['doctor_id']['_oid'],
      patientId: json['patient_id']['_oid'],
      recordCiphertext: json['record_ciphertext'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': {'_oid': id},
      'doctor_id': {'_oid': doctorId},
      'patient_id': {'_oid': patientId},
      'record_ciphertext': recordCiphertext,
    };
  }
}
