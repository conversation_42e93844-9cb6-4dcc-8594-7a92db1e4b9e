import datetime
from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import json

admin_bp = Blueprint('admin_bp', __name__)


@admin_bp.route('/pending-doctors', methods=['GET'])
@jwt_required()
def get_pending_doctors():
    db = admin_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    user = db.users.find_one({"_id": current_user_id})
    if 'admin' not in user.get('roles', []):
        return jsonify({"msg": "Admin access required"}), 403

    pipeline = [
        {'$match': {'professional_profile.type': 'doctor', 'professional_profile.verification_status': 'pending'}},
        {'$project': {'password_hash': 0, 'email': 0}}
    ]
    pending_doctors = list(db.users.aggregate(pipeline))

    return json.loads(json_util.dumps(pending_doctors)), 200


@admin_bp.route('/pending-delivery-providers', methods=['GET'])
@jwt_required()
def get_pending_delivery_providers():
    db = admin_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    user = db.users.find_one({"_id": current_user_id})
    if 'admin' not in user.get('roles', []):
        return jsonify({"msg": "Admin access required"}), 403

    pipeline = [
        {'$match': {'professional_profile.type': 'delivery_provider', 'professional_profile.verification_status': 'pending'}},
        {'$project': {'password_hash': 0, 'email': 0}}
    ]
    pending_providers = list(db.users.aggregate(pipeline))

    return json.loads(json_util.dumps(pending_providers)), 200


@admin_bp.route('/verify-doctor/<doctor_id>', methods=['POST'])
@jwt_required()
def verify_doctor(doctor_id):
    db = admin_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    user = db.users.find_one({"_id": current_user_id})
    if 'admin' not in user.get('roles', []):
        return jsonify({"msg": "Admin access required"}), 403

    data = request.get_json()
    approve = data.get('approve')
    if approve is None:
        return jsonify({"msg": "Missing 'approve' field"}), 400

    status = 'verified' if approve else 'rejected'
    db.users.update_one(
        {'_id': ObjectId(doctor_id)},
        {'$set': {'professional_profile.verification_status': status}}
    )
    return jsonify({"msg": f"Doctor has been {status}"}), 200


@admin_bp.route('/verify-delivery-provider/<provider_id>', methods=['POST'])
@jwt_required()
def verify_delivery_provider(provider_id):
    db = admin_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    user = db.users.find_one({"_id": current_user_id})
    if 'admin' not in user.get('roles', []):
        return jsonify({"msg": "Admin access required"}), 403

    data = request.get_json()
    approve = data.get('approve')
    if approve is None:
        return jsonify({"msg": "Missing 'approve' field"}), 400

    status = 'verified' if approve else 'rejected'
    db.delivery_providers.update_one(
        {'user_id': ObjectId(provider_id)},
        {'$set': {'verification_status': status}}
    )
    # Also update the user document
    db.users.update_one(
        {'_id': ObjectId(provider_id)},
        {'$set': {'professional_profile.verification_status': status}}
    )
    return jsonify({"msg": f"Delivery provider has been {status}"}), 200


@admin_bp.route('/pending-business-claims', methods=['GET'])
@jwt_required()
def get_pending_business_claims():
    db = admin_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    user = db.users.find_one({"_id": current_user_id})
    if 'admin' not in user.get('roles', []):
        return jsonify({"msg": "Admin access required"}), 403

    pipeline = [
        {'$match': {'status': 'pending'}},
        {'$lookup': {
            'from': 'users',
            'localField': 'user_id',
            'foreignField': '_id',
            'as': 'user_details'
        }},
        {'$unwind': '$user_details'},
        {'$lookup': {
            'from': 'places',
            'localField': 'place_id',
            'foreignField': '_id',
            'as': 'place_details'
        }},
        {'$unwind': '$place_details'},
        {'$project': {
            'user_details.password_hash': 0,
            'user_details.email': 0
        }}
    ]
    pending_claims = list(db.business_claims.aggregate(pipeline))

    return json.loads(json_util.dumps(pending_claims)), 200


@admin_bp.route('/verify-business-claim/<claim_id>', methods=['POST'])
@jwt_required()
def verify_business_claim(claim_id):
    db = admin_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    user = db.users.find_one({"_id": current_user_id})
    if 'admin' not in user.get('roles', []):
        return jsonify({"msg": "Admin access required"}), 403

    data = request.get_json()
    approve = data.get('approve')
    if approve is None:
        return jsonify({"msg": "Missing 'approve' field"}), 400

    status = 'approved' if approve else 'rejected'
    db.business_claims.update_one(
        {'_id': ObjectId(claim_id)},
        {'$set': {'status': status}}
    )

    if approve:
        claim = db.business_claims.find_one({"_id": ObjectId(claim_id)})
        place = db.places.find_one({'_id': claim['place_id']})
        new_business = {
            "owner_id": claim['user_id'],
            "business_name": place.get('name'),
            "description": f"Official business page for {place.get('name')}.",
            "images": place.get('photos', []),
            "created_at": datetime.datetime.now(datetime.timezone.utc),
            "linked_place_id": place.get('_id')
        }
        insert_result = db.businesses.insert_one(new_business)
        new_business_id = insert_result.inserted_id
        db.places.update_one(
            {'_id': claim['place_id']},
            {"$set": {"business_id": new_business_id}}
        )

    return jsonify({"msg": f"Business claim has been {status}"}), 200
