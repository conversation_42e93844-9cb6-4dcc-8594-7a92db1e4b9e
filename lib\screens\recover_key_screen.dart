import 'package:flutter/material.dart';
import 'package:wicker/services/crypto_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class RecoverKeyScreen extends StatefulWidget {
  const RecoverKeyScreen({super.key});

  @override
  _RecoverKeyScreenState createState() => _RecoverKeyScreenState();
}

class _RecoverKeyScreenState extends State<RecoverKeyScreen> {
  final CryptoService _cryptoService = CryptoService();
  final _mnemonicController = TextEditingController();
  bool _isLoading = false;

  void _recoverKey() async {
    if (_mnemonicController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your mnemonic phrase')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _cryptoService.recoverKeyFromMnemonic(_mnemonicController.text.trim());
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Account recovered successfully! You can now log in.')),
      );
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Recover Account'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Enter your 12-word mnemonic phrase to recover your account.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            NeuTextFormField(
              controller: _mnemonicController,
              labelText: 'Mnemonic Phrase',
              maxLines: 3,
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _recoverKey,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Recover'),
            ),
          ],
        ),
      ),
    );
  }
}
