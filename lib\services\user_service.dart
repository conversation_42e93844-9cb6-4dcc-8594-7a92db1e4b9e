import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/cache_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient
import 'package:wicker/models/user.dart';

class UserService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;
  final AuthService _auth = AuthService();
  final CacheService _cache = CacheService(); // <-- ADD THIS INSTANCE

  // --- REFACTORED: Now with caching ---
  Future<Map<String, dynamic>> getProfileHeader() async {
    const cacheKey = 'profileHeader';

    // 1. Try to get data from cache first
    final cachedData = await _cache.get(cacheKey);
    if (cachedData != null) {
      return cachedData as Map<String, dynamic>;
    }

    // 2. If cache miss, fetch from network
    print('CACHE MISS for key: $cacheKey. Fetching from network.');
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/users/profile-header'),
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        // 3. Save the fresh data to cache for 15 minutes
        await _cache.set(cacheKey, data, const Duration(minutes: 15));
        return data;
      } else {
        throw Exception('Failed to load profile header data');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Fetches a combined list of a user's posts and created places.
  Future<List<Map<String, dynamic>>> getMyContributions() async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/users/my-contributions'),
      );
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load contributions');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Updates the user's text-based profile information.
  Future<void> updateProfile({
    required String username,
    required String bio,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/users/profile/update'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.fields['username'] = username;
    request.fields['bio'] = bio;

    final response = await request.send();
    if (response.statusCode != 200) {
      throw Exception('Failed to update profile');
    }
  }

  // In lib/services/user_service.dart -> class UserService

  // --- NEW: Method to get a profile by user ID ---
  Future<Map<String, dynamic>> getUserProfile(String userId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/users/$userId/profile'),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load user profile');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Updates the user's avatar image.
  Future<void> updateAvatar(XFile image) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/users/profile/update'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.files.add(
      await http.MultipartFile.fromPath('profile_pic', image.path),
    );

    final response = await request.send();
    if (response.statusCode != 200) {
      throw Exception('Failed to update avatar');
    }
  }

  // Add this method inside your UserService class
  Future<void> applyForRole(String role) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/users/apply-for-role'),
      body: jsonEncode({'role': role}),
    );

    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to apply for role');
    }
  }

  Future<List<User>> getFollowers(String userId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/users/$userId/followers'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => User.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get followers');
    }
  }

  Future<List<User>> getFollowing(String userId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/users/$userId/following'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => User.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get following');
    }
  }
}
