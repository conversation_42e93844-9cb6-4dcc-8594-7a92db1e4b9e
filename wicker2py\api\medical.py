from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

medical_bp = Blueprint('medical_bp', __name__)

@medical_bp.route('/records', methods=['POST'])
@jwt_required()
def create_medical_record():
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    user = db.users.find_one({"_id": current_user_id})
    if 'doctor' not in user.get('roles', []):
        return jsonify({"msg": "You must have the 'doctor' role to create a medical record"}), 403

    patient_id = data.get('patient_id')
    record_ciphertext = data.get('record_ciphertext')

    if not patient_id or not record_ciphertext:
        return jsonify({"msg": "patient_id and record_ciphertext are required"}), 400

    new_record = {
        "doctor_id": current_user_id,
        "patient_id": ObjectId(patient_id),
        "record_ciphertext": record_ciphertext,
    }

    try:
        db.medical_records.insert_one(new_record)
        return jsonify({"msg": "Medical record created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@medical_bp.route('/records/patient/<patient_id>', methods=['GET'])
@jwt_required()
def get_medical_records_for_patient(patient_id):
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    patient_id_obj = ObjectId(patient_id)

    try:
        user = db.users.find_one({"_id": current_user_id})
        user_roles = user.get('roles', [])

        # Patient can access their own records
        if current_user_id == patient_id_obj:
            records = list(db.medical_records.find({"patient_id": patient_id_obj}))
            from bson import json_util
            import json
            return json.loads(json_util.dumps(records)), 200

        # Doctor can access records they have created for the patient
        if 'doctor' in user_roles:
            records = list(db.medical_records.find({"patient_id": patient_id_obj, "doctor_id": current_user_id}))
            from bson import json_util
            import json
            return json.loads(json_util.dumps(records)), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@medical_bp.route('/records/doctor', methods=['GET'])
@jwt_required()
def get_medical_records_for_doctor():
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        user = db.users.find_one({"_id": current_user_id})
        if 'doctor' not in user.get('roles', []):
            return jsonify({"msg": "You must have the 'doctor' role to access this page"}), 403

        records = list(db.medical_records.find({"doctor_id": current_user_id}))
        from bson import json_util
        import json
        return json.loads(json_util.dumps(records)), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@medical_bp.route('/summarize-consultation', methods=['POST'])
@jwt_required()
def summarize_consultation():
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    user = db.users.find_one({"_id": current_user_id})
    if 'doctor' not in user.get('roles', []):
        return jsonify({"msg": "You must have the 'doctor' role to perform this action"}), 403

    conversation = data.get('conversation')
    if not conversation:
        return jsonify({"msg": "conversation is required"}), 400

    try:
        from app import openai_client
        prompt = f"Summarize the following consultation between a doctor and a patient. The summary should be concise and include the main symptoms, diagnosis, and treatment plan.\n\nConversation:\n{conversation}"

        completion = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.5,
        )
        
        summary = completion.choices[0].message.content
        return jsonify({"summary": summary}), 200

    except Exception as e:
        return jsonify({"msg": "Failed to generate AI summary.", "error": str(e)}), 500


@medical_bp.route('/prescriptions', methods=['POST'])
@jwt_required()
def create_prescription():
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    user = db.users.find_one({"_id": current_user_id})
    if 'doctor' not in user.get('roles', []):
        return jsonify({"msg": "You must have the 'doctor' role to perform this action"}), 403

    patient_id = data.get('patient_id')
    medication = data.get('medication')
    dosage = data.get('dosage')
    instructions = data.get('instructions')

    if not all([patient_id, medication, dosage, instructions]):
        return jsonify({"msg": "patient_id, medication, dosage, and instructions are required"}), 400

    new_prescription = {
        "doctor_id": current_user_id,
        "patient_id": ObjectId(patient_id),
        "medication": medication,
        "dosage": dosage,
        "instructions": instructions,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
    }

    try:
        db.prescriptions.insert_one(new_prescription)
        return jsonify({"msg": "Prescription created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@medical_bp.route('/prescriptions/patient/<patient_id>', methods=['GET'])
@jwt_required()
def get_prescriptions_for_patient(patient_id):
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    patient_id_obj = ObjectId(patient_id)

    try:
        user = db.users.find_one({"_id": current_user_id})
        user_roles = user.get('roles', [])

        # Patient can access their own prescriptions
        if current_user_id == patient_id_obj:
            prescriptions = list(db.prescriptions.find({"patient_id": patient_id_obj}))
            from bson import json_util
            import json
            return json.loads(json_util.dumps(prescriptions)), 200

        # Doctor can access prescriptions they have created for the patient
        if 'doctor' in user_roles:
            prescriptions = list(db.prescriptions.find({"patient_id": patient_id_obj, "doctor_id": current_user_id}))
            from bson import json_util
            import json
            return json.loads(json_util.dumps(prescriptions)), 200

        return jsonify({"msg": "You don't have permission to access these prescriptions"}), 403

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@medical_bp.route('/prescriptions/doctor', methods=['GET'])
@jwt_required()
def get_prescriptions_for_doctor():
    db = medical_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        user = db.users.find_one({"_id": current_user_id})
        if 'doctor' not in user.get('roles', []):
            return jsonify({"msg": "You must have the 'doctor' role to access this page"}), 403

        prescriptions = list(db.prescriptions.find({"doctor_id": current_user_id}))
        from bson import json_util
        import json
        return json.loads(json_util.dumps(prescriptions)), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

