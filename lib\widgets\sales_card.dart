import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/screens/chat_screen.dart';
import 'package:wicker/services/time_ago_service.dart';
import 'package:wicker/services/transaction_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class SalesCard extends StatefulWidget {
  final Map<String, dynamic> salesData;
  final VoidCallback onUpdate;

  const SalesCard({super.key, required this.salesData, required this.onUpdate});

  @override
  State<SalesCard> createState() => _SalesCardState();
}

class _SalesCardState extends State<SalesCard> {
  final TransactionService _transactionService = TransactionService();
  final ImagePicker _picker = ImagePicker();
  bool _isUpdating = false;

  Future<void> _handleMarkAsDelivered() async {
    final XFile? image = await _picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 70,
    );
    if (image == null) return;

    setState(() => _isUpdating = true);

    try {
      final orderId = widget.salesData['_id']['\$oid'];
      await _transactionService.markAsDelivered(orderId, image);
      widget.onUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() => _isUpdating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // --- REFACTORED: Use null-safe access for all data points ---
    final status = widget.salesData['status'] as String? ?? 'pending';
    final totalAmount =
        (widget.salesData['total_amount'] as num?)?.toDouble() ?? 0.0;
    final orderDate = TimeAgoService.format(
      widget.salesData['created_at']?['\$date'] as String?,
    );
    final buyerDetails =
        widget.salesData['buyer_details'] as Map<String, dynamic>?;
    final buyerName = buyerDetails?['username'] as String? ?? 'Buyer';
    final items = widget.salesData['items'] as List<dynamic>? ?? [];
    final orderIdMap = widget.salesData['_id'] as Map<String, dynamic>?;
    final orderId = orderIdMap?['\$oid'] as String?;

    Color statusColor;
    IconData statusIcon;
    switch (status.toLowerCase()) {
      case 'paid':
        statusColor = Colors.blue;
        statusIcon = EvaIcons.clockOutline;
        break;
      case 'delivered':
        statusColor = Colors.purple;
        statusIcon = EvaIcons.paperPlaneOutline;
        break;
      case 'completed':
        statusColor = Colors.green;
        statusIcon = EvaIcons.checkmarkCircle2;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = EvaIcons.questionMarkCircleOutline;
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(EvaIcons.person, color: Colors.grey[700]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Sale to: $buyerName',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              NeuChip(
                label: status.toUpperCase(),
                icon: statusIcon,
                backgroundColor: statusColor,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'Order #${widget.salesData['_id']['\$oid'].substring(0, 8)}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ),

          const Divider(height: 24, thickness: 2, color: Colors.black),
          GestureDetector(
            // --- REFACTORED: Null-safe onTap handler ---
            onTap: () {
              if (buyerDetails != null && orderId != null) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        ChatScreen(roomId: orderId, recipientName: buyerName),
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Error: Cannot open chat. Order data is missing.',
                    ),
                  ),
                );
              }
            },
            child: const NeuCard(
              margin: EdgeInsets.only(bottom: 8),
              padding: EdgeInsets.symmetric(vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(EvaIcons.messageSquareOutline, size: 20),
                  SizedBox(width: 8),
                  Text(
                    "Message Buyer",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ),

          ...items.map((item) => _buildSalesItem(item)).toList(),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(orderDate, style: const TextStyle(color: Colors.grey)),
              Text(
                'Total: GHS ${totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Color(0xFF6C5CE7),
                ),
              ),
            ],
          ),
          if (status == 'paid')
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: GestureDetector(
                onTap: _isUpdating ? null : _handleMarkAsDelivered,
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  backgroundColor: const Color(0xFF6C5CE7),
                  child: Center(
                    child: _isUpdating
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'Mark as Delivered (with Proof)',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSalesItem(Map<String, dynamic> item) {
    final details = item['product_details'] as Map<String, dynamic>? ?? {};
    final name = details['product_name'] as String? ?? 'N/A';
    final quantity = item['quantity'] as int? ?? 0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text('•', style: TextStyle(color: Colors.grey[700], fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(child: Text(name, style: const TextStyle(fontSize: 14))),
          Text(
            'x$quantity',
            style: TextStyle(
              color: Colors.grey[800],
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
