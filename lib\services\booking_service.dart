import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class BookingService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<void> setAvailability({
    required String businessId,
    required Map<String, dynamic> schedule,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/availability/$businessId'),
      body: jsonEncode({'availability': schedule}),
    );

    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to set availability');
    }
  }

  Future<List<String>> getAvailability({
    required String businessId,
    required String serviceId,
    required DateTime date,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final dateString = DateFormat('yyyy-MM-dd').format(date);

    final uri = Uri.parse(
      '$baseUrl/api/bookings/availability/$businessId',
    ).replace(queryParameters: {'date': dateString, 'service_id': serviceId});

    final response = await _client.get(uri);

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<String>.from(data);
    } else {
      throw Exception('Failed to load availability');
    }
  }

  Future<void> requestBooking({
    required String businessId,
    required String serviceId,
    required DateTime startTime,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/request'),
      body: jsonEncode({
        'business_id': businessId,
        'service_id': serviceId,
        'start_time': startTime.toIso8601String(),
      }),
    );

    if (response.statusCode != 201) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to send booking request');
    }
  }

  // --- NEW: Method to fetch a vendor's schedule ---
  Future<List<Map<String, dynamic>>> getMySchedule(String businessId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/bookings/my-schedule/$businessId'),
    );
    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data);
    } else {
      throw Exception('Failed to load schedule');
    }
  }

  // --- NEW: Method to confirm a booking ---
  Future<void> confirmBooking(String bookingId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/$bookingId/confirm'),
    );
    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to confirm booking');
    }
  }

  // --- NEW: Method to decline a booking ---
  Future<void> declineBooking(String bookingId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/$bookingId/decline'),
    );
    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to decline booking');
    }
  }

  // In lib/services/booking_service.dart, add these new methods to the class

  Future<void> completeBooking(String bookingId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/$bookingId/complete'),
    );
    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to complete booking');
    }
  }

  Future<void> markAsNoShow(String bookingId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/$bookingId/no-show'),
    );
    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to mark as no-show');
    }
  }

  // In lib/services/booking_service.dart, add this new method to the class

  Future<String> getDailyBriefing(
    List<Map<String, dynamic>> dailyBookings,
  ) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/api/bookings/daily-briefing'),
        body: jsonEncode({'bookings': dailyBookings}),
      );

      if (response.statusCode == 200) {
        final body = jsonDecode(response.body);
        return body['briefing'];
      } else {
        // Don't throw a fatal error for an enhancement feature.
        // Just return a graceful fallback message.
        return "Could not load today's summary.";
      }
    } catch (e) {
      print("Daily briefing service error: $e");
      return "Could not load today's summary.";
    }
  }

  // Add these methods inside the BookingService class

  Future<List<Map<String, dynamic>>> getMyAppointments() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/bookings/my-appointments'),
    );
    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data);
    } else {
      throw Exception('Failed to load appointments');
    }
  }

  Future<bool> hasNotifications() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/bookings/notifications'),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body)['has_notifications'];
    }
    return false;
  }

  Future<void> markBookingsAsViewed() async {
    final baseUrl = await _config.getBaseUrl();
    await _client.post(Uri.parse('$baseUrl/api/bookings/mark-viewed'));
  }
}
