import 'dart:convert';
import 'package:wicker/models/medical_record.dart';
import 'package:wicker/models/prescription.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class MedicalService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<void> createMedicalRecord(String patientId, String recordCiphertext) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/medical/records'),
      body: jsonEncode({
        'patient_id': patientId,
        'record_ciphertext': recordCiphertext,
      }),
    );

    if (response.statusCode != 201) {
      throw Exception('Failed to create medical record');
    }
  }

  Future<List<MedicalRecord>> getMedicalRecordsForPatient(String patientId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/medical/records/patient/$patientId'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => MedicalRecord.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get medical records');
    }
  }

  Future<List<MedicalRecord>> getMedicalRecordsForDoctor() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/medical/records/doctor'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => MedicalRecord.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get medical records');
    }
  }

  Future<String> summarizeConsultation(String conversation) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/medical/summarize-consultation'),
      body: jsonEncode({'conversation': conversation}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['summary'];
    } else {
      throw Exception('Failed to summarize consultation');
    }
  }

  Future<void> createPrescription(String patientId, String medication, String dosage, String instructions) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/medical/prescriptions'),
      body: jsonEncode({
        'patient_id': patientId,
        'medication': medication,
        'dosage': dosage,
        'instructions': instructions,
      }),
    );

    if (response.statusCode != 201) {
      throw Exception('Failed to create prescription');
    }
  }

  Future<List<Prescription>> getPrescriptionsForPatient(String patientId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/medical/prescriptions/patient/$patientId'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => Prescription.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get prescriptions');
    }
  }

  Future<List<Prescription>> getPrescriptionsForDoctor() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/medical/prescriptions/doctor'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => Prescription.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get prescriptions');
    }
  }
}
