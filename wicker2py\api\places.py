import json
import os
from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
from werkzeug.utils import secure_filename
import uuid

places_bp = Blueprint('places_bp', __name__)

def get_db_collections(db):
    places_collection = db.places
    users_collection = db.users
    return places_collection, users_collection

# --- THE FIX: Updated the main route handler ---
@places_bp.route('/', methods=['GET', 'POST']) # 1. Allow POST requests
@jwt_required()
def handle_places():
    db = places_bp.db
    # 2. Check which HTTP method was used
    if request.method == 'POST':
        return add_place(db) # 3. Call the add_place function
    else: # Default to GET
        return get_explore_content(db)

# Note: The function now accepts 'db' as an argument
def add_place(db):
    places_collection, users_collection = get_db_collections(db)
    current_user_id = get_jwt_identity()
    data = request.form

    if not data or not data.get('name') or not data.get('location'):
        return jsonify({"msg": "Missing required fields: name, location"}), 400
    
    # --- Image Handling ---
    uploaded_photo_paths = []
    if 'images' in request.files:
        images = request.files.getlist('images')
        for image in images:
            if image.filename != '':
                filename = secure_filename(image.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads' 
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                image_path = os.path.join(upload_folder, unique_filename)
                
                image.save(image_path)
                uploaded_photo_paths.append(image_path)

    # --- Gamification Points ---
    points_awarded = 0
    if data.get('name'): points_awarded += 10
    if data.get('category'): points_awarded += 10
    if data.get('review'): points_awarded += 5
    if data.get('rating'): points_awarded += 5
    if uploaded_photo_paths: points_awarded += len(uploaded_photo_paths) * 2

    location_data = json.loads(data.get('location'))

    # --- Document Creation ---
    new_place = {
        "name": data.get('name'),
        "category": data.get('category'),
        "location": {
            "type": "Point",
            "coordinates": [float(location_data['longitude']), float(location_data['latitude'])]
        },
        "photos": uploaded_photo_paths,
        "created_by": ObjectId(current_user_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "reviews": [],
        "overall_rating": 0,
        "likes": [], # Add likes array for new places
        "dislikes": [] # Add dislikes array for new places
    }

    if data.get('review') or data.get('rating'):
        initial_review = {
            "user_id": ObjectId(current_user_id),
            "rating": float(data.get('rating', 0)),
            "review_text": data.get('review', ""),
            "created_at": datetime.datetime.now(datetime.timezone.utc)
        }
        new_place["reviews"].append(initial_review)
        new_place["overall_rating"] = float(data.get('rating', 0))

    try:
        places_collection.insert_one(new_place)
        users_collection.update_one(
            {'_id': ObjectId(current_user_id)},
            {'$inc': {'points': points_awarded}}
        )
        return jsonify({"msg": f"Place added! You earned {points_awarded} points."}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    


# @places_bp.route('/<place_id>/claim', methods=['POST'])
# @jwt_required()
# def claim_place(place_id):
#     """
#     Allows a user to claim a place they created, linking it to their business.
#     """
#     db = places_bp.db
#     current_user_id = ObjectId(get_jwt_identity())
    
#     # 1. Find the user's business. They must have one to claim a place.
#     business = db.businesses.find_one({"owner_id": current_user_id})
#     if not business:
#         return jsonify({"msg": "You must create a business profile first before claiming a place."}), 400

#     # 2. Find the place...
#     place = db.places.find_one({"_id": ObjectId(place_id)})
#     if not place:
#         return jsonify({"msg": "Place not found."}), 404
        
#     # 3. ...and verify the current user is the original creator.
#     if place.get('created_by') != current_user_id:
#         return jsonify({"msg": "You can only claim places that you originally created."}), 403

#     # 4. Check if the place is already claimed.
#     if place.get('business_id'):
#         return jsonify({"msg": "This place has already been claimed."}), 409

#     # 5. Link the place to the business.
#     try:
#         db.places.update_one(
#             {"_id": ObjectId(place_id)},
#             {"$set": {"business_id": business['_id']}}
#         )
#         return jsonify({"msg": "Place successfully claimed and linked to your business!"}), 200
#     except Exception as e:
#         return jsonify({"msg": "An error occurred during the claim process", "error": str(e)}), 500



# In flask_api/api/places.py

@places_bp.route('/<place_id>/claim', methods=['POST'])
@jwt_required()
def claim_place(place_id):
    """
    Allows a user to claim a place they created. This action CREATES a new
    business profile linked to the place, ensuring a separate inventory.
    """
    db = places_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    # 1. Find the place to be claimed
    place = db.places.find_one({"_id": ObjectId(place_id)})
    if not place:
        return jsonify({"msg": "Place not found."}), 404
        
    # 2. Verify the current user is the original creator
    if place.get('created_by') != current_user_id:
        return jsonify({"msg": "You can only claim places that you originally created."}), 403

    # 3. Check if the place is already linked to a business
    if place.get('business_id'):
        return jsonify({"msg": "This place has already been claimed."}), 409

    # 4. Create a NEW business document from the place's details
    try:
        new_business = {
            "owner_id": current_user_id,
            "business_name": place.get('name'),  # Inherit name
            "description": f"Official business page for {place.get('name')}.", # Default description
            "images": place.get('photos', []),      # Inherit photos
            "created_at": datetime.datetime.now(datetime.timezone.utc),
            "linked_place_id": place.get('_id')   # Create a link back to the original place
        }
        
        insert_result = db.businesses.insert_one(new_business)
        new_business_id = insert_result.inserted_id
        
        # 5. Link the original place to the NEWLY created business
        db.places.update_one(
            {"_id": ObjectId(place_id)},
            {"$set": {"business_id": new_business_id}}
        )
        
        return jsonify({"msg": f"'{place.get('name')}' successfully claimed! It's now listed under your businesses."}), 200
        
    except Exception as e:
        return jsonify({"msg": "An error occurred during the claim process", "error": str(e)}), 500











# In api/places.py

# This function replaces get_all_places
# def get_explore_content(db):
#     """Fetches a combined list of all public posts and places for the Explore page."""
#     posts_collection = db.posts
#     places_collection = db.places

#     # Get all posts
#     posts_pipeline = [
#         {'$addFields': {'content_type': 'post'}},
#         # You can add more stages here, like lookups for author details
#     ]
#     all_posts = list(posts_collection.aggregate(posts_pipeline))

#     # Get all places
#     places_pipeline = [
#         {'$addFields': {'content_type': 'place'}},
#     ]
#     all_places = list(places_collection.aggregate(places_pipeline))

#     # Combine, sort, and return
#     combined_content = sorted(all_posts + all_places, key=lambda x: x['created_at'], reverse=True)
#     return Response(json_util.dumps(combined_content), mimetype='application/json')

# --- THE FIX: This function now attaches full author_details ---
def get_explore_content(db):
    """Fetches a combined list of all public posts and places, ensuring
    author_details are included for every item."""
    posts_collection = db.posts
    places_collection = db.places

    # Pipeline to fetch and format posts with author details
    posts_pipeline = [
        {'$lookup': {
            'from': 'users', 'localField': 'author_id',
            'foreignField': '_id', 'as': 'author_details'
        }},
        {'$unwind': '$author_details'},
        {'$addFields': {'content_type': 'post'}}
    ]
    all_posts = list(posts_collection.aggregate(posts_pipeline))

    # Pipeline to fetch and format places with author details
    places_pipeline = [
        {'$lookup': {
            'from': 'users', 'localField': 'created_by',
            'foreignField': '_id', 'as': 'author_details'
        }},
        {'$unwind': '$author_details'},
        {'$addFields': {'content_type': 'place'}}
    ]
    all_places = list(places_collection.aggregate(places_pipeline))

    # Combine, sort, and return
    combined_content = sorted(all_posts + all_places, key=lambda x: x['created_at'], reverse=True)
    return Response(json_util.dumps(combined_content), mimetype='application/json')
# --- End of FIX ---


    





# In api/places.py

# @places_bp.route('/<place_id>', methods=['GET'])
# @jwt_required()
# def get_place_details(place_id):
#     """
#     Fetches the details for a single place, and also finds all posts
#     that have been tagged with this place's ID.
#     """

#     db = places_bp.db
#     places_collection = db.places
#     current_user_id = ObjectId(get_jwt_identity())
    
#     try:
#         pipeline = [
#             {'$match': {'_id': ObjectId(place_id)}},
#             {
#                 '$lookup': {
#                     'from': 'posts',
#                     'localField': '_id',
#                     'foreignField': 'tagged_place_id',
#                     'as': 'tagged_posts',
#                     # Add a sub-pipeline to check for likes/dislikes
#                     'pipeline': [
#                         {
#                             '$addFields': {
#                                 'isLiked': {'$in': [current_user_id, {'$ifNull': ['$likes', []]}]},
#                                 'isDisliked': {'$in': [current_user_id, {'$ifNull': ['$dislikes', []]}]}
#                             }
#                         }
#                     ]
#                 }
#             }
#         ]
        
#         result = list(places_collection.aggregate(pipeline))
        
#         if not result:
#             return jsonify({"msg": "Place not found"}), 404
#         print('results', result)
#         return Response(json_util.dumps(result[0]), mimetype='application/json')
#     except Exception as e:
#         return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    



@places_bp.route('/<place_id>', methods=['GET'])
@jwt_required()
def get_place_details(place_id):
    """
    Fetches details for a single place, posts tagged with the place,
    and a list of other businesses by the same owner ONLY if the place is claimed.
    """
    db = places_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    try:
        # First, fetch the primary place data
        place = db.places.find_one({'_id': ObjectId(place_id)})
        if not place:
            return jsonify({"msg": "Place not found"}), 404

        # Fetch posts tagged with this place
        posts_pipeline = [
            {'$match': {'tagged_place_id': ObjectId(place_id)}},
            {'$lookup': {
                'from': 'users',
                'localField': 'author_id',
                'foreignField': '_id',
                'as': 'author_details'
            }},
            {'$unwind': '$author_details'},
            {'$addFields': {
                'isLiked': {'$in': [current_user_id, {'$ifNull': ['$likes', []]}]},
                'isDisliked': {'$in': [current_user_id, {'$ifNull': ['$dislikes', []]}]}
            }}
        ]
        tagged_posts = list(db.posts.aggregate(posts_pipeline))
        place['tagged_posts'] = tagged_posts

        # # --- REFACTORED: Only fetch other businesses if the place IS claimed ---
        # place['other_businesses'] = [] # Initialize with an empty list by default
        
        # claimed_business_id = place.get('business_id')
        
        # # Only proceed if the place has a linked business_id
        # if claimed_business_id:
        #     # 1. Find the business that this place is linked to
        #     claimed_business = db.businesses.find_one({'_id': ObjectId(claimed_business_id)})
            
        #     if claimed_business:

        #         owner_id = claimed_business.get('owner_id')
                
        #         # 2. Find all other businesses by that same owner
        #         other_businesses_cursor = db.businesses.find({
        #             "owner_id": owner_id,
        #             "_id": {'$ne': ObjectId(claimed_business_id)} # Exclude the current one
        #         })
                
        #         place['other_businesses'] = list(other_businesses_cursor)
        #         #log the list of places
        #         print('other_businesses', place['other_businesses'])
        # # --- End of REFACTOR ---


        # --- REFACTORED: Fetch ALL businesses by the owner ---
        place['other_businesses'] = []
        
        claimed_business_id = place.get('business_id')
        
        if claimed_business_id:
            claimed_business = db.businesses.find_one({'_id': ObjectId(claimed_business_id)})
            if claimed_business:
                owner_id = claimed_business.get('owner_id')
                # Find all businesses by that owner, without any exclusions.
                all_businesses_cursor = db.businesses.find({"owner_id": owner_id})
                place['other_businesses'] = list(all_businesses_cursor)
        # --- End of REFACTOR ---

        return Response(json_util.dumps(place), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500




@places_bp.route('/<place_id>/like', methods=['POST'])
@jwt_required()
def like_place(place_id):
    db = places_bp.db
    places_collection = db.places
    current_user_id = ObjectId(get_jwt_identity())
    place_object_id = ObjectId(place_id)

    # Remove user from dislikes if they are switching their vote
    places_collection.update_one(
        {'_id': place_object_id},
        {'$pull': {'dislikes': current_user_id}}
    )

    # Add user to likes (if not already there)
    places_collection.update_one(
        {'_id': place_object_id},
        {'$addToSet': {'likes': current_user_id}}
    )
    return jsonify({"msg": "place liked"}), 200

@places_bp.route('/<place_id>/dislike', methods=['POST'])
@jwt_required()
def dislike_place(place_id):
    db = places_bp.db
    places_collection = db.places
    current_user_id = ObjectId(get_jwt_identity())
    place_object_id = ObjectId(place_id)

    # Remove user from likes if they are switching their vote
    places_collection.update_one(
        {'_id': place_object_id},
        {'$pull': {'likes': current_user_id}}
    )

    # Add user to dislikes (if not already there)
    places_collection.update_one(
        {'_id': place_object_id},
        {'$addToSet': {'dislikes': current_user_id}}
    )
    return jsonify({"msg": "place disliked"}), 200