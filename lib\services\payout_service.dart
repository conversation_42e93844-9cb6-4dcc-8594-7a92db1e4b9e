import 'dart:convert';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class PayoutService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<List<dynamic>> getBanks() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(Uri.parse('$baseUrl/api/payouts/banks'));
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to load bank list');
    }
  }

  Future<Map<String, dynamic>?> getCurrentRecipient() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/payouts/recipient'),
    );
    if (response.statusCode == 200) {
      // The backend returns null with a 200 status if no recipient exists
      if (response.body == 'null') {
        return null;
      }
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get current recipient');
    }
  }

  Future<void> saveRecipient({
    required String name,
    required String accountNumber,
    required String bankCode,
    required String type,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/payouts/recipient'),
      body: jsonEncode({
        'name': name,
        'account_number': accountNumber,
        'bank_code': bankCode,
        'type': type,
      }),
    );

    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to save payout details');
    }
  }
}
