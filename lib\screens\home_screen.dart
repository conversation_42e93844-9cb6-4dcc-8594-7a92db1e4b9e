import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wicker/models/place.dart';
import 'package:wicker/services/recommendation_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/place_detail_card.dart';
import 'package:wicker/widgets/real_search_bar.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/services/post_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PostService _postService = PostService();
  final RecommendationService _recommendationService = RecommendationService();
  Future<List<Map<String, dynamic>>>? _postsFuture;
  Future<List<Map<String, dynamic>>>? _recommendationsFuture;
  String _searchQuery = '';
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _postsFuture = _postService.getPosts();
    _recommendationsFuture = _recommendationService.getRecommendations();
  }

  void _onSearch(String query) {
    setState(() {
      _searchQuery = query;
      _selectedCategory = null;
      if (query.isEmpty) {
        _postsFuture = _postService.getPosts();
      } else {
        _postsFuture = _postService.searchPosts(query);
      }
    });
  }

  Future<void> _refresh() async {
    setState(() {
      if (_searchQuery.isEmpty) {
        _postsFuture = _postService.getPosts();
      } else {
        _postsFuture = _postService.searchPosts(_searchQuery);
      }
      _recommendationsFuture = _recommendationService.getRecommendations();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
            child: RealSearchBar(onSearch: _onSearch),
          ),
          _buildFilterChips(),
          Expanded(child: _buildBodyContent()),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _recommendationsFuture,
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const SizedBox.shrink();
        }
        final recommendations = snapshot.data!;
        return SizedBox(
          height: 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: recommendations.length,
            itemBuilder: (context, index) {
              final category = recommendations[index]['category'];
              final bool isSelected = _selectedCategory == category;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = isSelected ? null : category;
                  });
                },
                child: NeuChip(
                  label: category,
                  icon: Icons.category,
                  backgroundColor: isSelected ? const Color(0xFF6C5CE7) : Colors.white,
                  textColor: isSelected ? Colors.white : Colors.black,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildBodyContent() {
    if (_selectedCategory != null) {
      return _buildRecommendedPlaces();
    } else {
      return _buildPostsFeed();
    }
  }

  Widget _buildPostsFeed() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _postsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No posts found.'));
        }

        final allPosts = snapshot.data!;

        return RefreshIndicator(
          onRefresh: _refresh,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: allPosts.length,
            itemBuilder: (context, index) {
              final post = allPosts[index];
              return PostCard(postData: post);
            },
          ),
        );
      },
    );
  }

  Widget _buildRecommendedPlaces() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _recommendationsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No recommendations found.'));
        }

        final recommendations = snapshot.data!;
        final places = recommendations
            .firstWhere((rec) => rec['category'] == _selectedCategory)['places']
            .map((placeData) => Place.fromJson(placeData))
            .toList();

        return RefreshIndicator(
          onRefresh: _refresh,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: places.length,
            itemBuilder: (context, index) {
              final place = places[index];
              return PlaceDetailCard(
                placeData: {
                  '_id': {'\$oid': place.id},
                  'name': place.name,
                  'category': place.category,
                },
                onClose: () {},
              );
            },
          ),
        );
      },
    );
  }
}