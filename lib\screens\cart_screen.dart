import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:wicker/screens/payment_webview_screen.dart';
import 'package:wicker/screens/set_location_screen.dart';
import 'package:wicker/services/cart_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/delivery_service.dart';
import 'package:wicker/services/transaction_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartService _cartService = CartService();
  final TransactionService _transactionService = TransactionService();
  final DeliveryService _deliveryService = DeliveryService(); // New service

  late Future<Map<String, dynamic>> _cartFuture;
  bool _isCheckingOut = false;

  // --- NEW: State for the dynamic delivery flow ---
  LatLng? _deliveryLocation;
  double? _deliveryFee;
  bool _isCalculatingFee = false;
  // ---

  @override
  void initState() {
    super.initState();
    _cartFuture = _cartService.getCart();
  }

  void _refreshCart() {
    setState(() {
      // Reset delivery state when cart is refreshed
      _deliveryLocation = null;
      _deliveryFee = null;
      _cartFuture = _cartService.getCart();
    });
  }

  // --- NEW: Navigates to the map screen ---
  void _navigateToSetLocation() async {
    final LatLng? result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SetLocationScreen()),
    );

    if (result != null && mounted) {
      setState(() {
        _deliveryLocation = result;
      });
      _calculateFee(result);
    }
  }

  // --- NEW: Calls the backend to calculate the delivery fee ---
  void _calculateFee(LatLng location) async {
    setState(() => _isCalculatingFee = true);
    try {
      // We need the business ID from the first item in the cart
      final cartData = await _cartFuture;
      final items = cartData['items'] as List<dynamic>? ?? [];
      if (items.isEmpty) throw Exception("Cart is empty");

      final businessId = items.first['product_details']['business_id']['\$oid'];

      final feeData = await _deliveryService.calculateFee(
        businessId: businessId,
        buyerLocation: location,
      );
      if (mounted) {
        setState(() {
          _deliveryFee = (feeData['delivery_fee'] as num).toDouble();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error calculating fee: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCalculatingFee = false);
      }
    }
  }

  // --- REFACTORED: Now includes delivery details ---
  void _proceedToCheckout() async {
    if (_isCheckingOut || _deliveryLocation == null || _deliveryFee == null)
      return;

    setState(() => _isCheckingOut = true);

    try {
      final authUrl = await _transactionService.initiateCartCheckout(
        deliveryFee: _deliveryFee!,
        deliveryLocation: _deliveryLocation!,
      );

      if (!mounted) return;

      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentWebViewScreen(authorizationUrl: authUrl),
        ),
      );

      if (result == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment successful! Verifying order...'),
              backgroundColor: Colors.blue,
            ),
          );
        }
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCheckingOut = false);
      }
    }
  }

  double _calculateSubtotal(List<dynamic> items) {
    double subtotal = 0.0;
    for (var item in items) {
      final price =
          (item['product_details']['price'] as num?)?.toDouble() ?? 0.0;
      final quantity = (item['quantity'] as int?) ?? 0;
      subtotal += price * quantity;
    }
    return subtotal;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'My Cart',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _cartFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          final List<dynamic> items = snapshot.data?['items'] ?? [];

          if (items.isEmpty) {
            return const Center(
              child: Text(
                'Your cart is empty.',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            );
          }

          final double subtotal = _calculateSubtotal(items);

          return Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    return _CartItemTile(
                      itemData: items[index],
                      onUpdate: _refreshCart,
                    );
                  },
                ),
              ),
              _buildCheckoutSection(subtotal, items),
            ],
          );
        },
      ),
    );
  }

  //   Widget _buildCheckoutSection(double subtotal) {
  //     return NeuCard(
  //       margin: const EdgeInsets.all(16),
  //       child: Column(
  //         children: [
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               const Text(
  //                 'Subtotal:',
  //                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
  //               ),
  //               Text(
  //                 'GHS ${subtotal.toStringAsFixed(2)}',
  //                 style: const TextStyle(
  //                   fontSize: 18,
  //                   fontWeight: FontWeight.bold,
  //                   color: Color(0xFF6C5CE7),
  //                 ),
  //               ),
  //             ],
  //           ),
  //           const SizedBox(height: 16),
  //           GestureDetector(
  //             onTap: _isCheckingOut ? null : _proceedToCheckout,
  //             child: NeuCard(
  //               margin: EdgeInsets.zero,
  //               backgroundColor: const Color(0xFF00D2D3),
  //               child: Center(
  //                 child: Padding(
  //                   padding: const EdgeInsets.symmetric(vertical: 12.0),
  //                   child: _isCheckingOut
  //                       ? const SizedBox(
  //                           height: 20,
  //                           width: 20,
  //                           child: CircularProgressIndicator(
  //                             strokeWidth: 2,
  //                             color: Colors.white,
  //                           ),
  //                         )
  //                       : const Text(
  //                           'Proceed to Checkout',
  //                           style: TextStyle(
  //                             color: Colors.white,
  //                             fontWeight: FontWeight.bold,
  //                             fontSize: 16,
  //                           ),
  //                         ),
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     );
  //   }
  // }

  // --- REFACTORED: This widget now handles the entire checkout flow ---
  Widget _buildCheckoutSection(double subtotal, List<dynamic> items) {
    final hasPhysicalItems = items.any(
      (item) => item['product_details']['product_type'] == 'physical',
    );
    final grandTotal = subtotal + (_deliveryFee ?? 0);

    return NeuCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Subtotal:', style: TextStyle(fontSize: 18)),
              Text(
                'GHS ${subtotal.toStringAsFixed(2)}',
                style: const TextStyle(fontSize: 18),
              ),
            ],
          ),
          if (hasPhysicalItems) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Delivery:', style: TextStyle(fontSize: 18)),
                _isCalculatingFee
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(
                        _deliveryFee != null
                            ? 'GHS ${_deliveryFee!.toStringAsFixed(2)}'
                            : 'Not Set',
                        style: const TextStyle(fontSize: 18),
                      ),
              ],
            ),
          ],
          const Divider(height: 24, thickness: 2, color: Colors.black),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total:',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Text(
                'GHS ${grandTotal.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF6C5CE7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildCheckoutButton(hasPhysicalItems),
        ],
      ),
    );
  }

  Widget _buildCheckoutButton(bool hasPhysicalItems) {
    // Case 1: No physical items, user can pay directly
    if (!hasPhysicalItems) {
      return _buildPayButton(onPressed: _proceedToCheckout);
    }
    // Case 2: Physical items, but location has not been set yet
    if (_deliveryLocation == null) {
      return GestureDetector(
        onTap: _navigateToSetLocation,
        child: const NeuCard(
          margin: EdgeInsets.zero,
          backgroundColor: Color(0xFF6C5CE7),
          child: Center(
            child: Text(
              'Set Delivery Location',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      );
    }
    // Case 3: Physical items, location is set, ready to pay
    return _buildPayButton(onPressed: _proceedToCheckout);
  }

  Widget _buildPayButton({required VoidCallback onPressed}) {
    return GestureDetector(
      onTap: _isCheckingOut ? null : onPressed,
      child: NeuCard(
        margin: EdgeInsets.zero,
        backgroundColor: const Color(0xFF00D2D3),
        child: Center(
          child: _isCheckingOut
              ? const SizedBox(
                  height: 24,
                  width: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                )
              : const Text(
                  'Proceed to Pay',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
        ),
      ),
    );
  }
}

class _CartItemTile extends StatelessWidget {
  final Map<String, dynamic> itemData;
  final VoidCallback onUpdate;
  final CartService _cartService = CartService();

  _CartItemTile({required this.itemData, required this.onUpdate});

  @override
  Widget build(BuildContext context) {
    final details = itemData['product_details'] as Map<String, dynamic>;
    final productId = details['_id']['\$oid'];
    final quantity = itemData['quantity'] as int;
    final List<dynamic> mediaList = details['media'] as List<dynamic>? ?? [];
    final String? imagePath = mediaList.isNotEmpty
        ? mediaList.first?.toString()
        : null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: NeuCard(
        margin: EdgeInsets.zero,
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            NeuCard(
              margin: EdgeInsets.zero,
              padding: EdgeInsets.zero,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(13),
                child: SizedBox(
                  width: 70,
                  height: 70,
                  child: imagePath != null
                      ? FutureBuilder<String>(
                          future: ConfigService.instance.getBaseUrl(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              final fullUrl =
                                  '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                              return Image.network(fullUrl, fit: BoxFit.cover);
                            }
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          },
                        )
                      : Container(
                          color: Colors.grey.shade200,
                          child: const Icon(
                            Icons.storefront,
                            color: Colors.grey,
                          ),
                        ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    details['product_name'] ?? 'No Name',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'GHS ${(details['price'] as num).toStringAsFixed(2)}',
                    style: const TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(EvaIcons.minusCircleOutline),
                        onPressed: quantity > 1
                            ? () async {
                                await _cartService.updateItemQuantity(
                                  productId,
                                  quantity - 1,
                                );
                                onUpdate();
                              }
                            : null,
                      ),
                      Text(
                        '$quantity',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      IconButton(
                        icon: const Icon(EvaIcons.plusCircleOutline),
                        onPressed: () async {
                          await _cartService.updateItemQuantity(
                            productId,
                            quantity + 1,
                          );
                          onUpdate();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            IconButton(
              icon: const Icon(EvaIcons.trash2Outline, color: Colors.red),
              onPressed: () async {
                await _cartService.removeFromCart(productId);
                onUpdate();
              },
            ),
          ],
        ),
      ),
    );
  }
}
