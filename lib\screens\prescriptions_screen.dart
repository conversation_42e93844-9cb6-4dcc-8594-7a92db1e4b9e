import 'package:flutter/material.dart';
import 'package:wicker/models/prescription.dart';
import 'package:wicker/services/medical_service.dart';
import 'package:wicker/widgets/prescription_card.dart';
import 'package:wicker/services/auth_service.dart';

class PrescriptionsScreen extends StatefulWidget {
  final String? patientId;
  const PrescriptionsScreen({super.key, this.patientId});

  @override
  _PrescriptionsScreenState createState() => _PrescriptionsScreenState();
}

class _PrescriptionsScreenState extends State<PrescriptionsScreen> {
  final MedicalService _medicalService = MedicalService();
  final AuthService _authService = AuthService();
  late Future<List<Prescription>> _prescriptionsFuture;

  @override
  void initState() {
    super.initState();
    _prescriptionsFuture = _fetchPrescriptions();
  }

  Future<List<Prescription>> _fetchPrescriptions() async {
    final roles = await _authService.getRoles();
    if (roles.contains('doctor')) {
      if (widget.patientId != null) {
        return _medicalService.getPrescriptionsForPatient(widget.patientId!);
      } else {
        return _medicalService.getPrescriptionsForDoctor();
      }
    } else {
      final userId = await _authService.getUserId();
      return _medicalService.getPrescriptionsForPatient(userId!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Prescriptions'),
      ),
      body: FutureBuilder<List<Prescription>>(
        future: _prescriptionsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No prescriptions found.'));
          }

          final prescriptions = snapshot.data!;

          return ListView.builder(
            itemCount: prescriptions.length,
            itemBuilder: (context, index) {
              final prescription = prescriptions[index];
              return PrescriptionCard(prescription: prescription);
            },
          );
        },
      ),
    );
  }
}
