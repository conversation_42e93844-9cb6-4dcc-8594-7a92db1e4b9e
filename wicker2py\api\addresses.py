from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

addresses_bp = Blueprint('addresses_bp', __name__)

@addresses_bp.route('', methods=['POST'])
@jwt_required()
def add_address():
    db = addresses_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    if not data or not data.get('address_ciphertext') or not data.get('address_label'):
        return jsonify({"msg": "Missing address_ciphertext or address_label"}), 400

    new_address = {
        "user_id": current_user_id,
        "address_label": data['address_label'],
        "address_ciphertext": data['address_ciphertext'],
    }

    try:
        db.addresses.insert_one(new_address)
        return jsonify({"msg": "Address added successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@addresses_bp.route('', methods=['GET'])
@jwt_required()
def get_addresses():
    db = addresses_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        addresses = list(db.addresses.find({"user_id": current_user_id}))
        # Use json_util to handle ObjectId serialization
        from bson import json_util
        import json
        return json.loads(json_util.dumps(addresses)), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@addresses_bp.route('/<address_id>', methods=['DELETE'])
@jwt_required()
def delete_address(address_id):
    db = addresses_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        result = db.addresses.delete_one({"_id": ObjectId(address_id), "user_id": current_user_id})
        if result.deleted_count == 1:
            return jsonify({"msg": "Address deleted successfully"}), 200
        else:
            return jsonify({"msg": "Address not found or you don't have permission to delete it"}), 404
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
