import 'package:flutter/material.dart';
import 'package:wicker/services/payout_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class PayoutSettingsScreen extends StatefulWidget {
  const PayoutSettingsScreen({super.key});

  @override
  State<PayoutSettingsScreen> createState() => _PayoutSettingsScreenState();
}

class _PayoutSettingsScreenState extends State<PayoutSettingsScreen> {
  final PayoutService _payoutService = PayoutService();
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _numberController = TextEditingController();

  late Future<List<dynamic>> _dataFuture;
  List<dynamic> _banks = [];
  Map<String, dynamic>? _currentRecipient;

  // --- REFACTORED: Store the selected bank's code as a String ---
  String? _selectedBankCode;
  String _accountType = 'nuban';

  @override
  void initState() {
    super.initState();
    _dataFuture = Future.wait([
      _payoutService.getBanks(),
      _payoutService.getCurrentRecipient(),
    ]);
  }

  void _saveRecipient() async {
    if (!(_formKey.currentState?.validate() ?? false) ||
        _selectedBankCode == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please fill all fields')));
      return;
    }

    try {
      await _payoutService.saveRecipient(
        name: _nameController.text,
        accountNumber: _numberController.text,
        bankCode: _selectedBankCode!,
        type: _accountType,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Payout details saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {
          _dataFuture = Future.wait([
            _payoutService.getBanks(),
            _payoutService.getCurrentRecipient(),
          ]);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: AppBar(title: const Text('Payout Settings')),
      body: FutureBuilder<List<dynamic>>(
        future: _dataFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          final rawBanks = snapshot.data![0] as List<dynamic>;
          final seenCodes = <String>{};
          _banks = rawBanks
              .where((bank) => seenCodes.add(bank['code']))
              .toList();

          _currentRecipient = snapshot.data![1] as Map<String, dynamic>?;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (_currentRecipient != null)
                  _buildCurrentRecipientCard(_currentRecipient!),
                _buildAddRecipientForm(),
              ],
            ),
          );
        },
      ),
    );
  }

  // Widget _buildCurrentRecipientCard(Map<String, dynamic> recipient) {
  //   final details = recipient['details'];
  //   return NeuCard(
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         const Text(
  //           'Current Payout Method',
  //           style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
  //         ),
  //         const Divider(height: 24),
  //         Text(details['account_name'] ?? 'N/A'),
  //         Text(
  //           '${details['bank_name']} - ****${(details['account_number'] as String).substring((details['account_number'] as String).length - 4)}',
  //         ),
  //         const SizedBox(height: 8),
  //         const Text(
  //           'This is where your earnings will be sent.',
  //           style: TextStyle(color: Colors.grey),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // In lib/screens/payout_settings_screen.dart

  Widget _buildCurrentRecipientCard(Map<String, dynamic> recipient) {
    final details = recipient['details'];
    // --- THE FIX: Get the account holder's name from the top-level 'name' field ---
    final accountName = recipient['name'] as String? ?? 'N/A';

    return NeuCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Current Payout Method',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const Divider(height: 24),
          // --- Use the corrected variable ---
          Text(
            accountName,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(
            '${details['bank_name']} - ****${(details['account_number'] as String).substring((details['account_number'] as String).length - 4)}',
          ),
          const SizedBox(height: 8),
          const Text(
            'This is where your earnings will be sent.',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAddRecipientForm() {
    return NeuCard(
      margin: EdgeInsets.only(top: _currentRecipient != null ? 16 : 0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _currentRecipient != null
                  ? 'Update Payout Method'
                  : 'Add Payout Method',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // --- REFACTORED: Dropdown now uses String as its value type ---
            DropdownButtonFormField<String>(
              value: _selectedBankCode,
              hint: const Text('Select Bank or MoMo Provider'),
              isExpanded: true,
              items: _banks.map((bank) {
                return DropdownMenuItem<String>(
                  value: bank['code'] as String, // Value is the unique code
                  child: Text(bank['name']),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedBankCode = value;
                  final selectedBankObject = _banks.firstWhere(
                    (b) => b['code'] == value,
                  );
                  _accountType =
                      (selectedBankObject['type'] ?? 'nuban') == 'mobile_money'
                      ? 'mobile_money'
                      : 'nuban';
                });
              },
            ),

            // --- End of REFACTOR ---
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Account Holder Name',
              ),
              validator: (v) => v!.isEmpty ? 'Name is required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _numberController,
              decoration: const InputDecoration(
                labelText: 'Account / Phone Number',
              ),
              keyboardType: TextInputType.number,
              validator: (v) => v!.isEmpty ? 'Number is required' : null,
            ),
            const SizedBox(height: 24),
            GestureDetector(
              onTap: _saveRecipient,
              child: const NeuCard(
                margin: EdgeInsets.zero,
                backgroundColor: Color(0xFF00D2D3),
                child: Center(
                  child: Text(
                    'Save Details',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
