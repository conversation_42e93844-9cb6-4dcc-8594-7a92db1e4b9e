class Address {
  final String id;
  final String userId;
  final String addressLabel;
  final String addressCiphertext;

  Address({
    required this.id,
    required this.userId,
    required this.addressLabel,
    required this.addressCiphertext,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      id: json['_id']['_oid'],
      userId: json['user_id']['_oid'],
      addressLabel: json['address_label'],
      addressCiphertext: json['address_ciphertext'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': {'_oid': id},
      'user_id': {'_oid': userId},
      'address_label': addressLabel,
      'address_ciphertext': addressCiphertext,
    };
  }
}
