import os
from flask import Flask, jsonify, send_from_directory
from dotenv import load_dotenv
import pymongo
import openai
from flask_bcrypt import Bcrypt
from flask_jwt_extended import J<PERSON>TManager, get_jwt_identity, jwt_required
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room # Correct import
import datetime
from bson import ObjectId, json_util
import json

# --- App Initialization ---
app = Flask(__name__)
CORS(app)
# Initialize SocketIO with the Flask app
socketio = SocketIO(app, cors_allowed_origins="*")

# --- Configurations ---
load_dotenv()
app.config["MONGO_URI"] = os.getenv('MONGO_URI')
app.config["DB_NAME"] = os.getenv('DB')
app.config["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
app.config["JWT_SECRET_KEY"] = os.getenv('JWT_SECRET_KEY')
bcrypt = Bcrypt(app)
jwt = JWTManager(app)

# --- Database & Services Setup ---
try:
    mongo_client = pymongo.MongoClient(app.config["MONGO_URI"])
    db = mongo_client[app.config["DB_NAME"]]
    mongo_client.admin.command('ping')
    print("✅ MongoDB connection successful.")

    openai_client = openai.OpenAI(api_key=app.config["OPENAI_API_KEY"])
    app.openai_client = openai_client
    print("✅ OpenAI client configured.")
except Exception as e:
    print(f"❌ Error during initialization: {e}")
    db = None
    openai_client = None

# --- Import and Register Blueprints ---
# NOTE: Blueprints must be imported AFTER the app is created
from api.auth import auth_bp 
from api.places import places_bp 
from api.playlists import playlists_bp 
from api.queues import queues_bp
from api.posts import posts_bp
from api.users import users_bp
from api.reports import reports_bp
from api.comments import comments_bp
from api.ecommerce import ecommerce_bp
from api.socials import socials_bp
from api.search import search_bp
from api.transactions import transactions_bp
from api.cart import cart_bp
from api.inventory import inventory_bp
from api.bookings import bookings_bp 
from api.chat import chat_bp
from api.notifications import notifications_bp 
from api.payouts import payouts_bp
from api.delivery import delivery_bp
from api.addresses import addresses_bp
from api.delivery_providers import delivery_providers_bp
from api.medical import medical_bp
from api.admin import admin_bp
from api.recommendations import recommendations_bp
from api.analytics import analytics_bp



# Register blueprints

if db is not None:
    auth_bp.db = db
    places_bp.db = db
    playlists_bp.db = db 
    queues_bp.db = db
    posts_bp.db = db
    users_bp.db = db
    reports_bp.db = db
    comments_bp.db = db
    ecommerce_bp.db = db
    socials_bp.db = db
    search_bp.db = db
    transactions_bp.db = db
    cart_bp.db = db
    inventory_bp.db = db
    bookings_bp.db = db
    chat_bp.db = db
    notifications_bp.db = db
    payouts_bp.db = db
    delivery_bp.db = db
    addresses_bp.db = db
    delivery_providers_bp.db = db
    medical_bp.db = db
    admin_bp.db = db
    recommendations_bp.db = db
    analytics_bp.db = db



    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(places_bp, url_prefix='/api/places')
    app.register_blueprint(playlists_bp, url_prefix='/api/playlists')
    app.register_blueprint(queues_bp, url_prefix='/api/queues')
    app.register_blueprint(posts_bp, url_prefix='/api/posts') 
    app.register_blueprint(users_bp, url_prefix='/api/users')
    app.register_blueprint(reports_bp, url_prefix='/api/reports')
    app.register_blueprint(comments_bp, url_prefix='/api/comments')
    app.register_blueprint(ecommerce_bp, url_prefix='/api/ecommerce')
    app.register_blueprint(socials_bp, url_prefix='/api/socials')
    app.register_blueprint(search_bp, url_prefix='/api/search')
    app.register_blueprint(transactions_bp, url_prefix='/api/transactions')
    app.register_blueprint(cart_bp, url_prefix='/api/cart')
    app.register_blueprint(inventory_bp, url_prefix='/api/inventory') 
    app.register_blueprint(bookings_bp, url_prefix='/api/bookings')
    app.register_blueprint(chat_bp, url_prefix='/api/chat')
    app.register_blueprint(notifications_bp, url_prefix='/api/notifications')
    app.register_blueprint(payouts_bp, url_prefix='/api/payouts')
    app.register_blueprint(delivery_bp, url_prefix='/api/delivery')
    app.register_blueprint(addresses_bp, url_prefix='/api/addresses')
    app.register_blueprint(delivery_providers_bp, url_prefix='/api/delivery-providers')
    app.register_blueprint(medical_bp, url_prefix='/api/medical')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(recommendations_bp, url_prefix='/api/recommendations')
    app.register_blueprint(analytics_bp, url_prefix='/api/analytics')
    
    print("✅ Blueprints registered successfully.")
else:
    print("❌ Could not register blueprints, DB connection failed.")

# --- Centralized Socket.IO Event Handlers ---
@socketio.on('join')
def on_join(data):
    room = data.get('room')
    if room:
        join_room(room)
        print(f"User has entered room: {room}")

@socketio.on('leave')
def on_leave(data):
    room = data.get('room')
    if room:
        leave_room(room)
        print(f"User has left room: {room}")

# In chat.py, replace the on_send_message function

@socketio.on('send_message')
def on_send_message(data):
    """Receives a message and broadcasts it with read tracking."""
    db = chat_bp.db
    room = data.get('room')
    author_id = ObjectId(data.get('author_id'))
    if not room: return

    message_doc = {
        "room_id": room,
        "author_id": author_id,
        "author_username": data.get('author_username'),
        "text": data.get('text'),
        "timestamp": datetime.datetime.now(datetime.timezone.utc),
        # --- NEW: Initialize with the author having read it ---
        "read_by": [author_id]
    }
    
    db.messages.insert_one(message_doc)
    emit('receive_message', json.loads(json_util.dumps(message_doc)), to=room)







# --- Standard Routes ---
@app.route('/')
def index():
    return jsonify({"status": "ok", "message": "Welcome to the Wicker API!"})

@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    return send_from_directory('uploads', filename)

@app.route('/api/health')
def health_check():
    return jsonify({"status": "healthy"}), 200

# --- Main Execution ---
if __name__ == '__main__':
    # Use socketio.run() to correctly start the server
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)