from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime

analytics_bp = Blueprint('analytics_bp', __name__)

@analytics_bp.route('/log-event', methods=['POST'])
@jwt_required()
def log_event():
    db = analytics_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    event_type = data.get('event_type')
    metadata = data.get('metadata')

    if not event_type:
        return jsonify({"msg": "event_type is required"}), 400

    new_event = {
        "user_id": current_user_id,
        "event_type": event_type,
        "metadata": metadata,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
    }

    try:
        db.analytics.insert_one(new_event)
        return jsonify({"msg": "Event logged successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
