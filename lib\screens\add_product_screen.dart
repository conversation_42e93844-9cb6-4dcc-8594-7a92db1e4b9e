import 'dart:io';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/media_preview_tile.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/step_progress_indicator.dart';

class AddProductScreen extends StatefulWidget {
  final String businessId;
  const AddProductScreen({super.key, required this.businessId});

  @override
  _AddProductScreenState createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final PageController _pageController = PageController();
  final _formKey = GlobalKey<FormState>();

  // Data Controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockCountController = TextEditingController(text: '1');
  final _durationController = TextEditingController(text: '60');
  // In the _AddProductScreenState class, add a new controller
  final _deliveryFeeController = TextEditingController();

  // State
  int _currentPage = 0;
  String _productType = 'physical';
  String? _productSubType = 'manufactured';
  String? _barcode;
  final List<XFile> _mediaFiles = [];
  bool _isLoading = false;

  final EcommerceService _ecommerceService = EcommerceService();
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockCountController.dispose();
    _durationController.dispose();
    _deliveryFeeController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 3) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _pickFromGallery() async {
    final List<XFile> pickedFiles = await _picker.pickMultipleMedia();
    if (pickedFiles.isNotEmpty) {
      setState(() {
        _mediaFiles.addAll(pickedFiles);
      });
    }
  }

  Future<void> _takeWithCamera() async {
    final XFile? pickedFile = await _picker.pickImage(
      source: ImageSource.camera,
    );
    if (pickedFile != null) {
      setState(() {
        _mediaFiles.add(pickedFile);
      });
    }
  }

  void _showMediaPickerOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFFFEF7F0),
      builder: (context) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(EvaIcons.cameraOutline),
                title: const Text('Use Camera'),
                onTap: () {
                  Navigator.of(context).pop();
                  _takeWithCamera();
                },
              ),
              ListTile(
                leading: const Icon(EvaIcons.imageOutline),
                title: const Text('Pick from Gallery'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickFromGallery();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _removeMedia(int index) {
    setState(() {
      _mediaFiles.removeAt(index);
    });
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please fill out all required fields in previous steps.',
          ),
        ),
      );
      return;
    }
    if (_mediaFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one media file.')),
      );
      return;
    }
    setState(() => _isLoading = true);

    try {
      await _ecommerceService.addProduct(
        businessId: widget.businessId,
        productName: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        productType: _productType,
        productSubType: _productSubType,
        barcode: _barcode,
        media: _mediaFiles,
        stockCount: int.parse(_stockCountController.text),
        // --- NEW: Conditionally pass the duration ---
        durationMinutes: _productType == 'service'
            ? int.tryParse(_durationController.text)
            : null,
        deliveryFee: _productType == 'physical'
            ? double.tryParse(_deliveryFeeController.text)
            : null,
      );

      _nextPage(); // Move to the success screen
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Add New Product',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: StepProgressIndicator(
                stepCount: 4,
                currentStep: _currentPage,
              ),
            ),
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (page) {
                  setState(() {
                    _currentPage = page;
                  });
                },
                children: [
                  _buildStep1(),
                  _buildStep2(),
                  _buildStep3(),
                  _buildSuccessStep(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStep1() {
    return _buildStepContainer(
      title: "What are you selling?",
      child: Column(
        children: [
          NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Product Name',
                border: InputBorder.none,
              ),
              validator: (value) =>
                  value!.isEmpty ? 'Please enter a name' : null,
            ),
          ),
          const SizedBox(height: 16),
          NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TextFormField(
              controller: _descriptionController,
              maxLines: 4,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: InputBorder.none,
              ),
              validator: (value) =>
                  value!.isEmpty ? 'Please enter a description' : null,
            ),
          ),
        ],
      ),
      onNext: () {
        if (_formKey.currentState!.validate()) {
          _nextPage();
        }
      },
    );
  }

  Widget _buildStep2() {
    return _buildStepContainer(
      title: "Set your price and type",
      child: Column(
        children: [
          NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextFormField(
              controller: _priceController,
              decoration: const InputDecoration(
                labelText: 'Price (GHS)',
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.number,
              validator: (value) =>
                  value!.isEmpty ? 'Please enter a price' : null,
            ),
          ),
          const SizedBox(height: 16),
          NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _productType,
                isExpanded: true,
                items: ['physical', 'digital', 'service']
                    .map(
                      (label) => DropdownMenuItem(
                        value: label,
                        child: Text(label.toUpperCase()),
                      ),
                    )
                    .toList(),
                onChanged: (value) =>
                    setState(() => _productType = value ?? 'physical'),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // --- NEW: Animated duration field ---
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Column(
              children: [
                if (_productType == 'service')
                  NeuCard(
                    key: const ValueKey('duration_field'),
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: TextFormField(
                      controller: _durationController,
                      decoration: const InputDecoration(
                        labelText: 'Duration (in minutes)',
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a duration';
                        }
                        if (int.tryParse(value) == null ||
                            int.parse(value) <= 0) {
                          return 'Enter a valid number of minutes';
                        }
                        return null;
                      },
                    ),
                  ),
                if (_productType == 'physical')
                  NeuCard(
                    key: const ValueKey('delivery_fee_field'),
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: TextFormField(
                      controller: _deliveryFeeController,
                      decoration: const InputDecoration(
                        labelText: 'Delivery Fee (GHS)',
                        hintText: 'e.g. 15.00',
                        border: InputBorder.none,
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      onNext: () {
        if (_formKey.currentState!.validate()) {
          _nextPage();
        }
      },
    );
  }

  Widget _buildStep3() {
    return _buildStepContainer(
      title: "Upload media",
      child: Expanded(
        child: _mediaFiles.isEmpty
            ? GestureDetector(
                onTap: _showMediaPickerOptions,
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          EvaIcons.imageOutline,
                          size: 48,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 8),
                        Text('Add Images / Videos'),
                      ],
                    ),
                  ),
                ),
              )
            : GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _mediaFiles.length + 1,
                itemBuilder: (context, index) {
                  if (index == _mediaFiles.length) {
                    return GestureDetector(
                      onTap: _showMediaPickerOptions,
                      child: const NeuCard(
                        margin: EdgeInsets.zero,
                        child: Center(child: Icon(EvaIcons.plus, size: 32)),
                      ),
                    );
                  }
                  return MediaPreviewTile(
                    mediaFile: _mediaFiles[index],
                    onRemove: () => _removeMedia(index),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildSuccessStep() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Lottie.network(
          //   'https://app.lottiefiles.com/animation/de7cc548-7421-4310-b695-a5a5f513188a',
          Lottie.asset(
            'assets/success_animation.json',
            height: 150,
            repeat: false,
            animate: true,
            frameRate: FrameRate.max,
          ),
          const SizedBox(height: 24),
          const Text(
            "Product Added!",
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            "${_nameController.text} is now available in your inventory.",
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 32),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(true),
            child: const NeuCard(
              margin: EdgeInsets.zero,
              child: Center(
                child: Text(
                  "Done",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContainer({
    required String title,
    required Widget child,
    VoidCallback? onNext,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          child,
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_currentPage > 0)
                GestureDetector(
                  onTap: _previousPage,
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    child: Icon(EvaIcons.arrowBack),
                  ),
                ),
              if (onNext != null)
                Expanded(
                  child: GestureDetector(
                    onTap: onNext,
                    child: const NeuCard(
                      margin: EdgeInsets.only(left: 16),
                      backgroundColor: Color(0xFF6C5CE7),
                      child: Center(
                        child: Text(
                          "Next",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              if (_currentPage == 2) // Submit button on the last step
                Expanded(
                  child: GestureDetector(
                    onTap: _isLoading ? null : _submit,
                    child: NeuCard(
                      margin: const EdgeInsets.only(left: 16),
                      backgroundColor: const Color(0xFF00D2D3),
                      child: Center(
                        child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                              )
                            : const Text(
                                "Finish & Add Product",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
