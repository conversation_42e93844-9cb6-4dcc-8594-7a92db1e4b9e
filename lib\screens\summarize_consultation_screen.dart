import 'package:flutter/material.dart';
import 'package:wicker/services/medical_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class SummarizeConsultationScreen extends StatefulWidget {
  const SummarizeConsultationScreen({super.key});

  @override
  _SummarizeConsultationScreenState createState() =>
      _SummarizeConsultationScreenState();
}

class _SummarizeConsultationScreenState
    extends State<SummarizeConsultationScreen> {
  final MedicalService _medicalService = MedicalService();
  final _conversationController = TextEditingController();
  String? _summary;
  bool _isLoading = false;

  void _getSummary() async {
    if (_conversationController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a conversation')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _summary = null;
    });

    try {
      final summary = await _medicalService
          .summarizeConsultation(_conversationController.text);
      setState(() {
        _summary = summary;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Summarize Consultation'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            NeuTextFormField(
              controller: _conversationController,
              labelText: 'Conversation',
              maxLines: 10,
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _getSummary,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Get Summary'),
            ),
            if (_summary != null)
              Padding(
                padding: const EdgeInsets.only(top: 32.0),
                child: NeuCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Summary', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                      const SizedBox(height: 8),
                      Text(_summary!),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
