import os
import uuid
from flask import Response, jsonify, Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from bson import ObjectId, json_util
import json

users_bp = Blueprint('users_bp', __name__)

@users_bp.route('/profile-header', methods=['GET'])
@jwt_required()
def get_profile_header():
    """
    Fetches all necessary data for the HubScreen profile header in one go:
    - User info (username, pic)
    - Follower/Following counts
    - Points
    - Real-time calculated sales balance
    """
    db = users_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    # Aggregation pipeline to calculate total sales for the user's business
    sales_pipeline = [
        {'$match': {'seller_id': current_user_id, 'status': 'paid'}},
        {'$group': {'_id': '$seller_id', 'total_balance': {'$sum': '$total_amount'}}}
    ]
    sales_result = list(db.orders.aggregate(sales_pipeline))
    balance = sales_result[0]['total_balance'] if sales_result else 0.0

    # Aggregation pipeline to get user profile and follow counts
    profile_pipeline = [
        {'$match': {'_id': current_user_id}},
        {'$lookup': {
            'from': 'follows', 'localField': '_id',
            'foreignField': 'follower_id', 'as': 'following'
        }},
        {'$lookup': {
            'from': 'follows', 'localField': '_id',
            'foreignField': 'following_id', 'as': 'followers'
        }},
        {'$addFields': {
            'following_count': {'$size': '$following'},
            'followers_count': {'$size': '$followers'},
            'balance': balance  # Add the calculated balance
        }},
        {'$project': {'password_hash': 0, 'email': 0, 'following': 0, 'followers': 0}}
    ]
    
    user_profile = list(db.users.aggregate(profile_pipeline))

    if not user_profile:
        return jsonify({"msg": "User not found"}), 404

    return json.loads(json_util.dumps(user_profile[0])), 200


@users_bp.route('/profile/update', methods=['POST'])
@jwt_required()
def update_my_profile():
    db = users_bp.db
    users_collection = db.users
    current_user_id = ObjectId(get_jwt_identity())
    
    data = request.form
    update_fields = {}

    if 'username' in data and data['username']:
        update_fields['username'] = data['username']
    if 'bio' in data:
        update_fields['bio'] = data['bio']

    if 'profile_pic' in request.files:
        image = request.files['profile_pic']
        if image.filename != '':
            filename = secure_filename(image.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            upload_folder = 'uploads/avatars'
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)
            
            image_path = os.path.join(upload_folder, unique_filename)
            image.save(image_path)
            update_fields['profile_pic_url'] = image_path

    if not update_fields:
        return jsonify({"msg": "No update fields provided"}), 400

    try:
        users_collection.update_one(
            {'_id': current_user_id},
            {'$set': update_fields}
        )
        return jsonify({"msg": "Profile updated successfully"}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

@users_bp.route('/my-contributions', methods=['GET'])
@jwt_required()
def get_my_contributions():
    """Fetches a combined list of a user's posts and created places."""
    db = users_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        posts_pipeline = [
            {'$match': {'author_id': current_user_id}},
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'author_id',
                    'foreignField': '_id',
                    'as': 'author_details'
                }
            },
            {'$unwind': '$author_details'},
            {'$addFields': {'contribution_type': 'post'}},
            {'$sort': {'created_at': -1}}
        ]
        user_posts = list(db.posts.aggregate(posts_pipeline))

        places_pipeline = [
            {'$match': {'created_by': current_user_id}},
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'created_by',
                    'foreignField': '_id',
                    'as': 'author_details'
                }
            },
            {'$unwind': '$author_details'},
            {'$addFields': {'contribution_type': 'place'}},
            {'$sort': {'created_at': -1}}
        ]
        user_places = list(db.places.aggregate(places_pipeline))

        all_contributions = sorted(
            user_posts + user_places,
            key=lambda x: x['created_at'],
            reverse=True
        )

        return Response(json_util.dumps(all_contributions), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

@users_bp.route('/<user_id>/profile', methods=['GET'])
@jwt_required()
def get_user_profile(user_id):
    """Fetches public profile data for a given user_id."""
    db = users_bp.db
    pipeline = [
        {'$match': {'_id': ObjectId(user_id)}},
        {'$lookup': {
            'from': 'follows', 'localField': '_id',
            'foreignField': 'follower_id', 'as': 'following'
        }},
        {'$lookup': {
            'from': 'follows', 'localField': '_id',
            'foreignField': 'following_id', 'as': 'followers'
        }},
        {'$addFields': {
            'following_count': {'$size': '$following'},
            'followers_count': {'$size': '$followers'}
        }},
        {'$project': {'password_hash': 0, 'email': 0, 'following': 0, 'followers': 0}}
    ]
    user_profile_list = list(db.users.aggregate(pipeline))

    if not user_profile_list:
        return jsonify({"msg": "User not found"}), 404

    return json.loads(json_util.dumps(user_profile_list[0])), 200


@users_bp.route('/apply-for-role', methods=['POST'])
@jwt_required()
def apply_for_role():
    """Allows a user to apply for a professional role (e.g., doctor, delivery_provider)."""
    db = users_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    
    role = data.get('role')
    if not role or role not in ['doctor', 'delivery_provider']: # Add other roles as needed
        return jsonify({"msg": "Invalid or missing role. Valid roles are 'doctor', 'delivery_provider'."}), 400

    user = db.users.find_one({"_id": current_user_id})
    if not user:
        return jsonify({"msg": "User not found"}), 404

    # Check if user has already applied
    if user.get('professional_profile', {}).get('verification_status') == 'pending':
        return jsonify({"msg": "You have an existing application pending review."}), 400

    update_fields = {
        "professional_profile.is_professional": True,
        "professional_profile.type": role,
        "professional_profile.verification_status": "pending"
    }

    db.users.update_one(
        {'_id': current_user_id},
        {
            '$set': update_fields,
            '$addToSet': {'roles': role} # Add the new role to the roles array
        }
    )

    return jsonify({"msg": f"Your application to become a {role} has been submitted for review."}), 200