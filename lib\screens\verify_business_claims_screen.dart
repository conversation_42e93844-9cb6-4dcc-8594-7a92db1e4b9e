import 'package:flutter/material.dart';
import 'package:wicker/models/business_claim.dart';
import 'package:wicker/services/admin_service.dart';
import 'package:wicker/widgets/business_claim_card.dart';

class VerifyBusinessClaimsScreen extends StatefulWidget {
  const VerifyBusinessClaimsScreen({super.key});

  @override
  _VerifyBusinessClaimsScreenState createState() =>
      _VerifyBusinessClaimsScreenState();
}

class _VerifyBusinessClaimsScreenState
    extends State<VerifyBusinessClaimsScreen> {
  final AdminService _adminService = AdminService();
  late Future<List<BusinessClaim>> _claimsFuture;

  @override
  void initState() {
    super.initState();
    _claimsFuture = _adminService.getPendingBusinessClaims();
  }

  void _verifyClaim(String claimId, bool approve) async {
    try {
      await _adminService.verifyBusinessClaim(claimId, approve);
      setState(() {
        _claimsFuture = _adminService.getPendingBusinessClaims();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Business Claims'),
      ),
      body: FutureBuilder<List<BusinessClaim>>(
        future: _claimsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No pending business claims.'));
          }

          final claims = snapshot.data!;
          return ListView.builder(
            itemCount: claims.length,
            itemBuilder: (context, index) {
              final claim = claims[index];
              return BusinessClaimCard(
                claim: claim,
                onVerify: _verifyClaim,
              );
            },
          );
        },
      ),
    );
  }
}
