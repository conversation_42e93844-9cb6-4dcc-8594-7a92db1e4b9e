import 'package:flutter/material.dart';
import 'package:wicker/models/prescription.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:intl/intl.dart';

class PrescriptionCard extends StatelessWidget {
  final Prescription prescription;

  const PrescriptionCard({super.key, required this.prescription});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: NeuCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(prescription.medication, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            const SizedBox(height: 8),
            Text('Dosage: ${prescription.dosage}'),
            const SizedBox(height: 8),
            Text('Instructions: ${prescription.instructions}'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Prescribed on:', style: TextStyle(color: Colors.grey[600])),
                Text(DateFormat.yMMMd().format(prescription.createdAt), style: TextStyle(color: Colors.grey[600])),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
