import 'package:flutter/material.dart';
import 'package:wicker/models/medical_record.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

import 'package:wicker/services/crypto_service.dart';

class MedicalRecordCard extends StatelessWidget {
  final MedicalRecord record;

  const MedicalRecordCard({super.key, required this.record});

  void _showDecryptedRecord(BuildContext context) async {
    final cryptoService = CryptoService();
    final decryptedRecord = await cryptoService.decrypt(record.recordCiphertext);
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Record ID: ${record.id}'),
          content: Text(decryptedRecord),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: NeuCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Record ID: ${record.id}', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('Patient ID: ${record.patientId}'),
            const SizedBox(height: 8),
            Text('Doctor ID: ${record.doctorId}'),
            const SizedBox(height: 8),
            NeuButton(
              onPressed: () => _showDecryptedRecord(context),
              child: const Text('Show Record'),
            ),
          ],
        ),
      ),
    );
  }
}
