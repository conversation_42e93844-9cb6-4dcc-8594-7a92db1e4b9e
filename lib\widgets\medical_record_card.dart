import 'package:flutter/material.dart';
import 'package:wicker/models/medical_record.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class MedicalRecordCard extends StatelessWidget {
  final MedicalRecord record;

  const MedicalRecordCard({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Neu<PERSON>ard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Record ID: ${record.id}', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('Patient ID: ${record.patientId}'),
            const SizedBox(height: 8),
            Text('Doctor ID: ${record.doctorId}'),
            const SizedBox(height: 8),
            const Text('Record:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text(record.recordCiphertext),
          ],
        ),
      ),
    );
  }
}
