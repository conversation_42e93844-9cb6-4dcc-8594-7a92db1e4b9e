import 'package:flutter/material.dart';
import 'package:wicker/models/address.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class AddressCard extends StatelessWidget {
  final Address address;
  final VoidCallback onDelete;

  const AddressCard({super.key, required this.address, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Neu<PERSON><PERSON>(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(address.addressLabel, style: const TextStyle(fontSize: 16)),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: onDelete,
            ),
          ],
        ),
      ),
    );
  }
}
