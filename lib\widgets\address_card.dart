import 'package:flutter/material.dart';
import 'package:wicker/models/address.dart';
import 'package:wicker/services/crypto_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class AddressCard extends StatelessWidget {
  final Address address;
  final VoidCallback onDelete;

  const AddressCard({super.key, required this.address, required this.onDelete});

  void _showDecryptedAddress(BuildContext context) async {
    final cryptoService = CryptoService();
    final decryptedAddress = await cryptoService.decrypt(address.addressCiphertext);
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(address.addressLabel),
          content: Text(decryptedAddress),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: NeuCard(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(address.addressLabel, style: const TextStyle(fontSize: 16)),
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.visibility, color: Colors.blue),
                  onPressed: () => _showDecryptedAddress(context),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: onDelete,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
