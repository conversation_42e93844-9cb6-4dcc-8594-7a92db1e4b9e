from flask import request, jsonify, Blueprint
import bcrypt
# Import create_refresh_token and the new decorator
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from bson import ObjectId

# We need access to the 'db' object from app.py
# We will pass it to the blueprint factory function
auth_bp = Blueprint('auth_bp', __name__)

# A helper function to get the users collection
def get_users_collection(db):
    return db.users

@auth_bp.route('/register', methods=['POST'])
def register():
    db = auth_bp.db # Access db instance from the blueprint
    users_collection = get_users_collection(db)
    data = request.get_json()


#Chapper, NiggaTron38

    # --- Input Validation ---
    if not data or not data.get('email') or not data.get('password') or not data.get('username'):
        return jsonify({"msg": "Missing email, username, or password"}), 400

    # --- Check for existing user ---
    if users_collection.find_one({'email': data['email']}):
        return jsonify({"msg": "Email already exists"}), 409
    if users_collection.find_one({'username': data['username']}):
        return jsonify({"msg": "Username already exists"}), 409

    # --- Password Hashing ---
    hashed_password = bcrypt.hashpw(data['password'].encode('utf-8'), bcrypt.gensalt())

    # --- Create User Document ---
    # Based on PRD sections 3.2 and 3.8
    new_user = {
        "username": data['username'],
        "email": data['email'],
        "password_hash": hashed_password,
        "bio": "",
        "profile_pic_url": "", # Default or placeholder URL
        "points": 0, # Starting points for gamification [cite: 3]
        "roles": ["user"], # Future-proofing for vendors, riders etc.
           # --- NEW: Add placeholder for professional profile ---
         "professional_profile": {
        "is_professional": False,
        "type": None, # e.g., 'doctor', 'lawyer'
        "verification_status": "unverified" # States: unverified, pending, verified
    }
    }
    
    # --- Insert into Database ---
    try:
        users_collection.insert_one(new_user)
        return jsonify({"msg": "User created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


# THE CHANGE: The login route now returns both an access and a refresh token
@auth_bp.route('/login', methods=['POST'])
def login():
    db = auth_bp.db
    users_collection = get_users_collection(db)
    data = request.get_json()

    if not data or not data.get('email') or not data.get('password'):
        return jsonify({"msg": "Missing email or password"}), 400

    user = users_collection.find_one({'email': data['email']})

    if user and bcrypt.checkpw(data['password'].encode('utf-8'), user['password_hash']):
        access_token = create_access_token(identity=str(user['_id']))
        refresh_token = create_refresh_token(identity=str(user['_id'])) # Create refresh token
        return jsonify(access_token=access_token, refresh_token=refresh_token), 200
    
    return jsonify({"msg": "Bad email or password"}), 401


# NEW ROUTE: This route uses a refresh token to issue a new access token
@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True) # This decorator requires a valid refresh token
def refresh():
    current_user = get_jwt_identity()
    new_access_token = create_access_token(identity=current_user)
    return jsonify(access_token=new_access_token), 200