import 'package:flutter/material.dart';
import 'package:wicker/models/address.dart';
import 'package:wicker/services/address_service.dart';
import 'package:wicker/services/delivery_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreateDeliveryOrderScreen extends StatefulWidget {
  const CreateDeliveryOrderScreen({super.key});

  @override
  _CreateDeliveryOrderScreenState createState() =>
      _CreateDeliveryOrderScreenState();
}

class _CreateDeliveryOrderScreenState extends State<CreateDeliveryOrderScreen> {
  final DeliveryService _deliveryService = DeliveryService();
  final AddressService _addressService = AddressService();

  late Future<List<Address>> _addressesFuture;
  Address? _selectedAddress;
  final _pickupLocationController = TextEditingController();
  final _itemDetailsController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addressesFuture = _addressService.getAddresses();
  }

  void _createOrder() async {
    if (_selectedAddress == null ||
        _pickupLocationController.text.isEmpty ||
        _itemDetailsController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all fields')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _deliveryService.createDeliveryOrder(
        _selectedAddress!.id,
        {'text': _pickupLocationController.text}, // Simple map for now
        {'description': _itemDetailsController.text}, // Simple map for now
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Delivery order created successfully')),
      );
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Delivery Order'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FutureBuilder<List<Address>>(
              future: _addressesFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return const Text('Could not load addresses');
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Text('No addresses found. Please add an address first.');
                }

                return NeuCard(
                  child: DropdownButtonFormField<Address>(
                    value: _selectedAddress,
                    items: snapshot.data!.map((Address address) {
                      return DropdownMenuItem<Address>(
                        value: address,
                        child: Text(address.addressLabel),
                      );
                    }).toList(),
                    onChanged: (Address? newValue) {
                      setState(() {
                        _selectedAddress = newValue;
                      });
                    },
                    decoration: const InputDecoration(
                      labelText: 'Select Recipient Address',
                      border: InputBorder.none,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            NeuTextFormField(
              controller: _pickupLocationController,
              labelText: 'Pickup Location',
            ),
            const SizedBox(height: 16),
            NeuTextFormField(
              controller: _itemDetailsController,
              labelText: 'Item Details',
              maxLines: 3,
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _createOrder,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Create Order'),
            ),
          ],
        ),
      ),
    );
  }
}
