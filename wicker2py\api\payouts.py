import os
import requests
from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

payouts_bp = Blueprint('payouts_bp', __name__)

PAYSTACK_SECRET_KEY = os.getenv('PAYSTACK_SECRET_KEY')
PAYSTACK_API_URL = "https://api.paystack.co"

@payouts_bp.route('/banks', methods=['GET'])
@jwt_required()
def get_banks():
    """
    Fetches the list of supported banks and mobile money providers for Ghana from Paystack.
    """
    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}"}
    # We can filter by type (momo_code, bank_code) and currency (GHS)
    params = {'country': 'ghana', 'currency': 'GHS'}
    
    try:
        response = requests.get(f"{PAYSTACK_API_URL}/bank", headers=headers, params=params)
        response.raise_for_status()
        return jsonify(response.json()['data']), 200
    except requests.exceptions.RequestException as e:
        return jsonify({"msg": "Could not fetch bank list from Paystack", "error": str(e)}), 503

@payouts_bp.route('/recipient', methods=['POST'])
@jwt_required()
def create_transfer_recipient():
    """
    Creates a Paystack Transfer Recipient and saves the recipient code to the user.
    """
    db = payouts_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    # Validate required fields
    required_fields = ['name', 'account_number', 'bank_code', 'type']
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields: name, account_number, bank_code, type"}), 400

    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
    payload = {
        "type": data['type'], # e.g., 'mobile_money' or 'nuban' for bank accounts
        "name": data['name'],
        "account_number": data['account_number'],
        "bank_code": data['bank_code'],
        "currency": "GHS"
    }

    try:
        # Create the recipient on Paystack's servers
        response = requests.post(f"{PAYSTACK_API_URL}/transferrecipient", headers=headers, json=payload)
        response.raise_for_status()
        recipient_data = response.json()['data']
        recipient_code = recipient_data['recipient_code']

        # Save the secure recipient code to the user's profile
        db.users.update_one(
            {"_id": current_user_id},
            {"$set": {"paystack_recipient_code": recipient_code}}
        )
        
        return jsonify({"msg": "Payout details saved successfully."}), 200

    except requests.exceptions.RequestException as e:
        return jsonify({"msg": "Failed to create payout recipient with Paystack", "error": str(e)}), 503

@payouts_bp.route('/recipient', methods=['GET'])
@jwt_required()
def get_transfer_recipient():
    """
    Retrieves the current user's saved (non-sensitive) payout details from Paystack.
    """
    db = payouts_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    user = db.users.find_one({"_id": current_user_id})
    if not user or 'paystack_recipient_code' not in user:
        return jsonify(None), 200 # Return null if no recipient code is saved

    recipient_code = user['paystack_recipient_code']
    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}"}

    try:
        response = requests.get(f"{PAYSTACK_API_URL}/transferrecipient/{recipient_code}", headers=headers)
        response.raise_for_status()
        
        # Return the details from Paystack
        return jsonify(response.json()['data']), 200
    except requests.exceptions.RequestException as e:
        return jsonify({"msg": "Failed to retrieve payout details from Paystack", "error": str(e)}), 503