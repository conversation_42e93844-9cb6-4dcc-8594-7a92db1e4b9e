import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';

class BubbleMapMarker extends StatelessWidget {
  final String placeName;
  final String? category;
  final double? rating;

  const BubbleMapMarker({
    super.key,
    required this.placeName,
    this.category,
    this.rating,
  });

  IconData _getIconForCategory(String? category) {
    switch (category) {
      case 'Restaurant':
        return PhosphorIcons.bowlSteam();
      case 'Cafe':
        return PhosphorIcons.coffee();
      case 'Shop':
        return PhosphorIcons.shoppingCart();
      case 'Art Gallery':
        return PhosphorIcons.palette();
      default:
        return PhosphorIcons.mapPinSimple();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 150, // Fixed width to ensure content fits
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // The Bubble containing the icon and rating
          CustomPaint(
            painter: _<PERSON>ub<PERSON>Pain<PERSON>(),
            child: Container(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 18),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getIconForCategory(category),
                    size: 20,
                    color: Colors.black,
                  ),
                  const SizedBox(width: 6),
                  if (rating != null && rating! > 0)
                    RatingBar.builder(
                      initialRating: rating!,
                      minRating: 1,
                      direction: Axis.horizontal,
                      allowHalfRating: true,
                      itemCount: 5,
                      itemSize: 14.0,
                      ignoreGestures: true,
                      itemBuilder: (context, _) =>
                          const Icon(Icons.star, color: Colors.amber),
                      onRatingUpdate: (rating) {},
                    ),
                ],
              ),
            ),
          ),
          // Place Name below the bubble
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black54,
                  offset: Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            child: Text(
              placeName,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

// CustomPainter to draw the speech bubble with a border
class _BubblePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = Colors.white;
    const tailHeight = 10.0;
    const tailWidth = 20.0;
    final bubbleHeight = size.height - tailHeight;

    // The main rectangle
    final path = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, size.width, bubbleHeight),
          const Radius.circular(16),
        ),
      );

    // The triangle tail
    path.moveTo(size.width / 2 - tailWidth / 2, bubbleHeight);
    path.lineTo(size.width / 2, size.height);
    path.lineTo(size.width / 2 + tailWidth / 2, bubbleHeight);
    path.close();

    // Draw shadow first
    canvas.drawShadow(path, Colors.black, 4.0, true);

    // Draw the bubble shape
    canvas.drawPath(path, paint);

    // Draw the border
    final borderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
