import 'dart:math';

import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class ConfirmBackupScreen extends StatefulWidget {
  final String mnemonic;

  const ConfirmBackupScreen({super.key, required this.mnemonic});

  @override
  _ConfirmBackupScreenState createState() => _ConfirmBackupScreenState();
}

class _ConfirmBackupScreenState extends State<ConfirmBackupScreen> {
  late List<String> _words;
  late List<int> _indicesToConfirm;
  late List<TextEditingController> _controllers;

  @override
  void initState() {
    super.initState();
    _words = widget.mnemonic.split(' ');
    _indicesToConfirm = _getRandomIndices(3, _words.length);
    _controllers = List.generate(3, (_) => TextEditingController());
  }

  List<int> _getRandomIndices(int count, int max) {
    final random = Random();
    final indices = <int>{};
    while (indices.length < count) {
      indices.add(random.nextInt(max));
    }
    return indices.toList()..sort();
  }

  void _confirm() {
    bool allCorrect = true;
    for (int i = 0; i < _indicesToConfirm.length; i++) {
      if (_controllers[i].text.trim().toLowerCase() !=
          _words[_indicesToConfirm[i]].toLowerCase()) {
        allCorrect = false;
        break;
      }
    }

    if (allCorrect) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Backup confirmed successfully!')),
      );
      Navigator.of(context).popUntil((route) => route.isFirst);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Incorrect words. Please try again.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Confirm Backup'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Please enter the specified words from your mnemonic phrase to confirm your backup.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ...List.generate(_indicesToConfirm.length, (i) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: NeuTextFormField(
                  controller: _controllers[i],
                  labelText: 'Word #${_indicesToConfirm[i] + 1}',
                ),
              );
            }),
            const Spacer(),
            NeuButton(
              onPressed: _confirm,
              child: const Text('Confirm'),
            ),
          ],
        ),
      ),
    );
  }
}
