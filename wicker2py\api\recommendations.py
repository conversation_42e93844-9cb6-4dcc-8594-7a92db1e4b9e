from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

recommendations_bp = Blueprint('recommendations_bp', __name__)

@recommendations_bp.route('/', methods=['GET'])
@jwt_required()
def get_recommendations():
    db = recommendations_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    # For now, we will use a simple logic to get the most viewed categories
    # In a real application, this would be a more complex recommendation algorithm
    pipeline = [
        {'$match': {'user_id': current_user_id, 'event_type': 'place_view', 'details.category': {'$ne': None}}},
        {'$group': {'_id': '$details.category', 'count': {'$sum': 1}}},
        {'$sort': {'count': -1}},
        {'$limit': 5}
    ]
    top_categories = list(db.analytics.aggregate(pipeline))

    recommendations = []
    for category in top_categories:
        places = list(db.places.find({'category': category['_id']}).limit(5))
        recommendations.append({
            'category': category['_id'],
            'places': places
        })

    from bson import json_util
    import json
    return json.loads(json_util.dumps(recommendations)), 200
