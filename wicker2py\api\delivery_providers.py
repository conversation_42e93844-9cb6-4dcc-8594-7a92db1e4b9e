from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

delivery_providers_bp = Blueprint('delivery_providers_bp', __name__)

@delivery_providers_bp.route('/register', methods=['POST'])
@jwt_required()
def register_delivery_provider():
    db = delivery_providers_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    user = db.users.find_one({"_id": current_user_id})
    if 'delivery_provider' not in user.get('roles', []):
        return jsonify({"msg": "You must have the 'delivery_provider' role to register"}), 403

    if db.delivery_providers.find_one({"user_id": current_user_id}):
        return jsonify({"msg": "You are already registered as a delivery provider"}), 400

    vehicle_details = data.get('vehicle_details')
    if not vehicle_details:
        return jsonify({"msg": "vehicle_details are required"}), 400

    new_provider = {
        "user_id": current_user_id,
        "vehicle_details": vehicle_details,
        "availability": "unavailable", # available, unavailable
        "verification_status": "pending", # pending, verified, rejected
    }

    try:
        db.delivery_providers.insert_one(new_provider)
        return jsonify({"msg": "Delivery provider registration successful. Awaiting verification."}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_providers_bp.route('', methods=['GET'])
@jwt_required()
def get_delivery_providers():
    db = delivery_providers_bp.db
    try:
        providers = list(db.delivery_providers.find({}))
        from bson import json_util
        import json
        return json.loads(json_util.dumps(providers)), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_providers_bp.route('/<provider_id>', methods=['GET'])
@jwt_required()
def get_delivery_provider(provider_id):
    db = delivery_providers_bp.db
    try:
        provider = db.delivery_providers.find_one({"_id": ObjectId(provider_id)})
        if not provider:
            return jsonify({"msg": "Delivery provider not found"}), 404
        
        from bson import json_util
        import json
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_providers_bp.route('/<provider_id>', methods=['PUT'])
@jwt_required()
def update_delivery_provider(provider_id):
    db = delivery_providers_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    try:
        provider = db.delivery_providers.find_one({"_id": ObjectId(provider_id)})
        if not provider:
            return jsonify({"msg": "Delivery provider not found"}), 404

        if provider['user_id'] != current_user_id:
            return jsonify({"msg": "You don't have permission to update this provider's details"}), 403

        update_fields = {}
        if 'availability' in data:
            update_fields['availability'] = data['availability']
        if 'vehicle_details' in data:
            update_fields['vehicle_details'] = data['vehicle_details']

        if not update_fields:
            return jsonify({"msg": "No update fields provided"}), 400

        db.delivery_providers.update_one(
            {"_id": ObjectId(provider_id)},
            {"$set": update_fields}
        )

        return jsonify({"msg": "Delivery provider details updated successfully"}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_providers_bp.route('/verify/<provider_id>', methods=['POST'])
@jwt_required()
def verify_delivery_provider(provider_id):
    db = delivery_providers_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    verification_status = data.get('verification_status')

    if not verification_status or verification_status not in ['verified', 'rejected']:
        return jsonify({"msg": "Invalid verification status. Must be 'verified' or 'rejected'."}), 400

    try:
        user = db.users.find_one({"_id": current_user_id})
        if 'admin' not in user.get('roles', []):
            return jsonify({"msg": "You don't have permission to perform this action"}), 403

        provider = db.delivery_providers.find_one({"_id": ObjectId(provider_id)})
        if not provider:
            return jsonify({"msg": "Delivery provider not found"}), 404

        db.delivery_providers.update_one(
            {"_id": ObjectId(provider_id)},
            {"$set": {"verification_status": verification_status}}
        )

        return jsonify({"msg": f"Delivery provider has been {verification_status}"}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
