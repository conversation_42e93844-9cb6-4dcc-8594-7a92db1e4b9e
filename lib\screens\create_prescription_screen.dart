import 'package:flutter/material.dart';
import 'package:wicker/services/medical_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreatePrescriptionScreen extends StatefulWidget {
  const CreatePrescriptionScreen({super.key});

  @override
  _CreatePrescriptionScreenState createState() =>
      _CreatePrescriptionScreenState();
}

class _CreatePrescriptionScreenState extends State<CreatePrescriptionScreen> {
  final MedicalService _medicalService = MedicalService();
  final _patientIdController = TextEditingController();
  final _medicationController = TextEditingController();
  final _dosageController = TextEditingController();
  final _instructionsController = TextEditingController();
  bool _isLoading = false;

  void _createPrescription() async {
    if (_patientIdController.text.isEmpty ||
        _medicationController.text.isEmpty ||
        _dosageController.text.isEmpty ||
        _instructionsController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all fields')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _medicalService.createPrescription(
        _patientIdController.text,
        _medicationController.text,
        _dosageController.text,
        _instructionsController.text,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Prescription created successfully')),
      );
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Prescription'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            NeuTextFormField(
              controller: _patientIdController,
              labelText: 'Patient ID',
            ),
            const SizedBox(height: 16),
            NeuTextFormField(
              controller: _medicationController,
              labelText: 'Medication',
            ),
            const SizedBox(height: 16),
            NeuTextFormField(
              controller: _dosageController,
              labelText: 'Dosage',
            ),
            const SizedBox(height: 16),
            NeuTextFormField(
              controller: _instructionsController,
              labelText: 'Instructions',
              maxLines: 3,
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _createPrescription,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Create Prescription'),
            ),
          ],
        ),
      ),
    );
  }
}
