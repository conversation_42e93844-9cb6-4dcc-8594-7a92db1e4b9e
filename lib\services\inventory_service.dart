import 'dart:convert';
import 'dart:io';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class InventoryService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<List<dynamic>> getRestockAlerts() async {
    final baseUrl = await _config.getBaseUrl();
    print('=== InventoryService.getRestockAlerts ===');
    print('Base URL: $baseUrl');

    try {
      final uri = Uri.parse('$baseUrl/api/inventory/restock-alerts');
      print('Request URI: $uri');

      final response = await _client.get(uri);
      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        print('Decoded response: $decoded');
        return decoded;
      } else {
        print('Error response: ${response.body}');
        throw Exception('Failed to load restock alerts');
      }
    } catch (e) {
      print('Get restock alerts error: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> analyzeShelfImage(File image) async {
    final baseUrl = await _config.getBaseUrl();
    print('=== InventoryService.analyzeShelfImage ===');
    print('Base URL: $baseUrl');
    print('Image path: ${image.path}');

    try {
      // Convert image to Base64 string for JSON transport
      final bytes = await image.readAsBytes();
      print('Image bytes length: ${bytes.length}');

      final String base64Image = base64Encode(bytes);
      print('Base64 image length: ${base64Image.length}');

      final uri = Uri.parse('$baseUrl/api/inventory/analyze-shelf');
      print('Request URI: $uri');

      final requestBody = jsonEncode({'image': base64Image});
      print('Request body length: ${requestBody.length}');

      final response = await _client.post(uri, body: requestBody);
      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        print('Decoded response: $decoded');
        return decoded;
      } else {
        final body = jsonDecode(response.body);
        print('Error response body: $body');
        throw Exception(body['msg'] ?? 'Failed to analyze shelf image');
      }
    } catch (e) {
      print('Analyze shelf image error: $e');
      rethrow;
    }
  }
}
