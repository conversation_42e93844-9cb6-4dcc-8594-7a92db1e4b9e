import 'dart:math';
import 'package:flutter/material.dart';

class GlowingBorderWrapper extends StatefulWidget {
  final Widget child;
  final bool showGlow;
  final double borderWidth;
  final Duration animationDuration;
  final double borderRadius;
  final List<Color> gradientColors;
  final double glowBlurRadius;

  const GlowingBorderWrapper({
    super.key,
    required this.child,
    this.showGlow = false,
    this.borderWidth = 3.0,
    this.animationDuration = const Duration(seconds: 2),
    this.borderRadius = 16.0,
    this.gradientColors = const [
      Color(0xFF6C5CE7), // Purple
      Color(0xFFFF6B6B), // Coral
      Color(0xFFFFE66D), // Yellow
      Color(0xFF4ECDC4), // Teal
      Color(0xFF6C5CE7), // Loop back to Purple
    ],
    this.glowBlurRadius = 8.0,
  });

  @override
  State<GlowingBorderWrapper> createState() => _GlowingBorderWrapperState();
}

class _GlowingBorderWrapperState extends State<GlowingBorderWrapper>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    if (widget.showGlow) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(covariant GlowingBorderWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showGlow != oldWidget.showGlow) {
      if (widget.showGlow) {
        _controller.repeat();
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: widget.showGlow
          ? AnimatedBuilder(
              animation: _animation,
              builder: (context, _) {
                return CustomPaint(
                  painter: _GlowingBorderPainter(
                    animation: _animation.value,
                    borderWidth: widget.borderWidth,
                    borderRadius: widget.borderRadius,
                    gradientColors: widget.gradientColors,
                    glowBlurRadius: widget.glowBlurRadius,
                  ),
                  child: widget.child,
                );
              },
            )
          : widget.child,
    );
  }
}

class _GlowingBorderPainter extends CustomPainter {
  final double animation;
  final double borderWidth;
  final double borderRadius;
  final List<Color> gradientColors;
  final double glowBlurRadius;

  _GlowingBorderPainter({
    required this.animation,
    required this.borderWidth,
    required this.borderRadius,
    required this.gradientColors,
    required this.glowBlurRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    // Create the gradient sweep
    final center = Offset(size.width / 2, size.height / 2);
    final angle = animation * 2 * pi;

    // Create a conical gradient that rotates
    final gradient = SweepGradient(
      colors: gradientColors,
      startAngle: angle,
      endAngle: angle + 2 * pi,
      center: Alignment.center,
    );

    // Paint with glow effect
    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..maskFilter = MaskFilter.blur(BlurStyle.outer, glowBlurRadius);

    // Draw the glowing border
    canvas.drawRRect(rrect, paint);

    // Draw a sharper border on top for definition
    final sharpPaint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    canvas.drawRRect(rrect, sharpPaint);

    // Optional: Add bright spots for enhanced glow effect
    _drawBrightSpots(canvas, size, angle);
  }

  void _drawBrightSpots(Canvas canvas, Size size, double angle) {
    // Calculate positions for bright spots along the border
    final spotCount = 3;
    final perimeter = 2 * (size.width + size.height);

    for (int i = 0; i < spotCount; i++) {
      final spotAngle = angle + (i * 2 * pi / spotCount);
      final progress = (spotAngle / (2 * pi)) % 1.0;
      final position = _getPositionOnRRect(size, progress);

      // Draw a bright spot
      final spotPaint = Paint()
        ..color = Colors.white.withOpacity(0.8)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

      canvas.drawCircle(position, borderWidth * 1.5, spotPaint);

      // Draw inner core of bright spot
      final corePaint = Paint()..color = Colors.white;

      canvas.drawCircle(position, borderWidth * 0.5, corePaint);
    }
  }

  Offset _getPositionOnRRect(Size size, double progress) {
    // Approximate position along the rounded rectangle perimeter
    final width = size.width;
    final height = size.height;
    final adjustedRadius = borderRadius.clamp(0.0, min(width, height) / 2);

    // Calculate perimeter sections
    final cornerLength = (pi / 2) * adjustedRadius;
    final straightWidth = width - 2 * adjustedRadius;
    final straightHeight = height - 2 * adjustedRadius;
    final totalPerimeter =
        4 * cornerLength + 2 * straightWidth + 2 * straightHeight;

    final distance = progress * totalPerimeter;
    var currentDistance = 0.0;

    // Top side
    if (distance <= straightWidth) {
      return Offset(adjustedRadius + distance, 0);
    }
    currentDistance += straightWidth;

    // Top-right corner
    if (distance <= currentDistance + cornerLength) {
      final cornerProgress = (distance - currentDistance) / cornerLength;
      final angle = cornerProgress * pi / 2;
      return Offset(
        width - adjustedRadius + adjustedRadius * sin(angle),
        adjustedRadius - adjustedRadius * cos(angle),
      );
    }
    currentDistance += cornerLength;

    // Right side
    if (distance <= currentDistance + straightHeight) {
      return Offset(width, adjustedRadius + (distance - currentDistance));
    }
    currentDistance += straightHeight;

    // Bottom-right corner
    if (distance <= currentDistance + cornerLength) {
      final cornerProgress = (distance - currentDistance) / cornerLength;
      final angle = pi / 2 + cornerProgress * pi / 2;
      return Offset(
        width - adjustedRadius + adjustedRadius * cos(angle - pi / 2),
        height - adjustedRadius + adjustedRadius * sin(angle - pi / 2),
      );
    }
    currentDistance += cornerLength;

    // Bottom side
    if (distance <= currentDistance + straightWidth) {
      return Offset(
        width - adjustedRadius - (distance - currentDistance),
        height,
      );
    }
    currentDistance += straightWidth;

    // Bottom-left corner
    if (distance <= currentDistance + cornerLength) {
      final cornerProgress = (distance - currentDistance) / cornerLength;
      final angle = pi + cornerProgress * pi / 2;
      return Offset(
        adjustedRadius - adjustedRadius * sin(angle - pi),
        height - adjustedRadius + adjustedRadius * cos(angle - pi),
      );
    }
    currentDistance += cornerLength;

    // Left side
    if (distance <= currentDistance + straightHeight) {
      return Offset(0, height - adjustedRadius - (distance - currentDistance));
    }
    currentDistance += straightHeight;

    // Top-left corner
    final remainingDistance = distance - currentDistance;
    final cornerProgress = remainingDistance / cornerLength;
    final angle = 3 * pi / 2 + cornerProgress * pi / 2;
    return Offset(
      adjustedRadius - adjustedRadius * cos(angle - 3 * pi / 2),
      adjustedRadius - adjustedRadius * sin(angle - 3 * pi / 2),
    );
  }

  @override
  bool shouldRepaint(_GlowingBorderPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}
