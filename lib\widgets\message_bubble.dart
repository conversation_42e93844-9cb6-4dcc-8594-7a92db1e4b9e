import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class MessageBubble extends StatelessWidget {
  final String text;
  final String sender;
  final bool isMe;

  const MessageBubble({
    super.key,
    required this.text,
    required this.sender,
    required this.isMe,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: isMe
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Text(
              sender,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ),
          NeuCard(
            margin: isMe
                ? const EdgeInsets.only(left: 80)
                : const EdgeInsets.only(right: 80),
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            backgroundColor: isMe ? const Color(0xFF6C5CE7) : Colors.white,
            child: Text(
              text,
              style: TextStyle(color: isMe ? Colors.white : Colors.black),
            ),
          ),
        ],
      ),
    );
  }
}
