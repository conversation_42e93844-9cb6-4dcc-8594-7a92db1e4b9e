import 'dart:convert';
import 'package:latlong2/latlong.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class DeliveryService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<Map<String, dynamic>> calculateFee({
    required String businessId,
    required LatLng buyerLocation,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/delivery/calculate-fee'),
      body: jsonEncode({
        'business_id': businessId,
        'buyer_location': {
          'lat': buyerLocation.latitude,
          'lon': buyerLocation.longitude,
        },
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to calculate delivery fee');
    }
  }
}
