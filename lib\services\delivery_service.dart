import 'dart:convert';
import 'package:latlong2/latlong.dart';
import 'package:wicker/models/delivery_order.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class DeliveryService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<Map<String, dynamic>> calculateFee({
    required String businessId,
    required LatLng buyerLocation,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/delivery/calculate-fee'),
      body: jsonEncode({
        'business_id': businessId,
        'buyer_location': {
          'lat': buyerLocation.latitude,
          'lon': buyerLocation.longitude,
        },
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to calculate delivery fee');
    }
  }

  Future<DeliveryOrder> createDeliveryOrder(String recipientAddressId, Map<String, dynamic> pickupLocation, Map<String, dynamic> itemDetails) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/delivery/orders'),
      body: jsonEncode({
        'recipient_address_id': recipientAddressId,
        'pickup_location': pickupLocation,
        'item_details': itemDetails,
      }),
    );

    if (response.statusCode == 201) {
      return DeliveryOrder.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to create delivery order');
    }
  }

  Future<List<DeliveryOrder>> getDeliveryOrders() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/delivery/orders'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => DeliveryOrder.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get delivery orders');
    }
  }

  Future<DeliveryOrder> getDeliveryOrder(String orderId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/delivery/orders/$orderId'),
    );

    if (response.statusCode == 200) {
      return DeliveryOrder.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to get delivery order');
    }
  }

  Future<void> updateDeliveryOrderStatus(String orderId, String status) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.put(
      Uri.parse('$baseUrl/api/delivery/orders/$orderId'),
      body: jsonEncode({
        'status': status,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update delivery order status');
    }
  }
}
