import 'package:flutter/material.dart';
import 'package:wicker/models/user.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class VerificationListScreen extends StatefulWidget {
  final String title;
  final Future<List<User>> usersFuture;
  final Function(String, bool) onVerify;

  const VerificationListScreen({
    super.key,
    required this.title,
    required this.usersFuture,
    required this.onVerify,
  });

  @override
  _VerificationListScreenState createState() => _VerificationListScreenState();
}

class _VerificationListScreenState extends State<VerificationListScreen> {
  late Future<List<User>> _usersFuture;

  @override
  void initState() {
    super.initState();
    _usersFuture = widget.usersFuture;
  }

  void _verifyUser(String userId, bool approve) async {
    try {
      await widget.onVerify(userId, approve);
      setState(() {
        _usersFuture = widget.usersFuture;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: FutureBuilder<List<User>>(
        future: _usersFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No pending verifications.'));
          }

          final users = snapshot.data!;
          return ListView.builder(
            itemCount: users.length,
            itemBuilder: (context, index) {
              final user = users[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundImage: user.profilePicUrl != null
                      ? NetworkImage(user.profilePicUrl!)
                      : null,
                  child: user.profilePicUrl == null
                      ? const Icon(Icons.person)
                      : null,
                ),
                title: Text(user.username),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    NeuButton(
                      onPressed: () => _verifyUser(user.id, true),
                      child: const Text('Approve'),
                      backgroundColor: Colors.green,
                    ),
                    const SizedBox(width: 8),
                    NeuButton(
                      onPressed: () => _verifyUser(user.id, false),
                      child: const Text('Reject'),
                      backgroundColor: Colors.red,
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
