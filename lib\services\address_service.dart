import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:wicker/models/address.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/config_service.dart';

class AddressService {
  final AuthService _authService = AuthService();
  final ConfigService _configService = ConfigService.instance;

  Future<void> addAddress(String addressCiphertext, String addressLabel) async {
    final accessToken = await _authService.getAccessToken();
    final baseUrl = await _configService.getBaseUrl();
    final response = await http.post(
      Uri.parse('$baseUrl/api/addresses'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode({
        'address_ciphertext': addressCiphertext,
        'address_label': addressLabel,
      }),
    );

    if (response.statusCode != 201) {
      throw Exception('Failed to add address');
    }
  }

  Future<List<Address>> getAddresses() async {
    final accessToken = await _authService.getAccessToken();
    final baseUrl = await _configService.getBaseUrl();
    final response = await http.get(
      Uri.parse('$baseUrl/api/addresses'),
      headers: {'Authorization': 'Bearer $accessToken'},
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => Address.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get addresses');
    }
  }

  Future<void> deleteAddress(String addressId) async {
    final accessToken = await _authService.getAccessToken();
    final baseUrl = await _configService.getBaseUrl();
    final response = await http.delete(
      Uri.parse('$baseUrl/api/addresses/$addressId'),
      headers: {'Authorization': 'Bearer $accessToken'},
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to delete address');
    }
  }
}
