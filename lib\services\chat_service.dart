import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class ChatService {
  final ConfigService _config = ConfigService.instance;
  final AuthService _auth = AuthService();
  final WickerHttpClient _client = WickerHttpClient(); // For fetching history

  IO.Socket? _socket;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get messages => _messageController.stream;

  Future<void> connect() async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    // Disconnect from any existing socket first
    disconnect();

    try {
      _socket = IO.io(
        baseUrl,
        IO.OptionBuilder()
            .setTransports(['websocket'])
            .disableAutoConnect()
            .setAuth({'token': token}) // Send token for authentication
            .build(),
      );

      _socket!.connect();

      _socket!.onConnect((_) {
        debugPrint('ChatService: Connected to server');
      });

      _socket!.on('receive_message', (data) {
        _messageController.add(data as Map<String, dynamic>);
      });

      _socket!.onDisconnect((_) => debugPrint('ChatService: Disconnected'));
    } catch (e) {
      debugPrint('ChatService connection error: $e');
    }
  }

  void joinRoom(String roomId) {
    _socket?.emit('join', {'room': roomId});
  }

  void leaveRoom(String roomId) {
    _socket?.emit('leave', {'room': roomId});
  }

  void sendMessage({
    required String text,
    required String roomId,
    required String authorId,
    required String authorUsername,
  }) {
    _socket?.emit('send_message', {
      'room': roomId,
      'text': text,
      'author_id': authorId,
      'author_username': authorUsername,
    });
  }

  Future<List<Map<String, dynamic>>> fetchHistory(String roomId) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/api/chat/history/$roomId'),
      );
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load chat history');
      }
    } catch (e) {
      debugPrint('Error fetching chat history: $e');
      return [];
    }
  }

  // Add this new method to your ChatService class

  Future<void> markMessagesAsRead(String roomId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      await _client.post(Uri.parse('$baseUrl/api/chat/$roomId/mark-read'));
    } catch (e) {
      print('Failed to mark messages as read: $e');
    }
  }

  void disconnect() {
    _socket?.dispose();
    _socket = null;
  }
}
