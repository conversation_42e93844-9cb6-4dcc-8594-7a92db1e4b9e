import 'dart:async';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/time_ago_service.dart';
import 'package:wicker/services/transaction_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/screens/chat_screen.dart';
import 'package:otp/otp.dart';

class OrderCard extends StatefulWidget {
  final Map<String, dynamic> orderData;
  final VoidCallback onUpdate;

  const OrderCard({super.key, required this.orderData, required this.onUpdate});

  @override
  State<OrderCard> createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> {
  final TransactionService _transactionService = TransactionService();
  final ImagePicker _picker = ImagePicker();
  bool _isConfirming = false;
  bool _isUploadingProof = false;

  String? _totpSecret;
  String _displayCode = "------";
  Timer? _totpTimer;
  bool _isLoadingSecret = true;

  @override
  void initState() {
    super.initState();
    _initializeTotp();
  }

  @override
  void dispose() {
    _totpTimer?.cancel(); // Important: cancel the timer to prevent memory leaks
    super.dispose();
  }

  Future<void> _initializeTotp() async {
    final status = widget.orderData['status'];
    if (status != 'paid' && status != 'delivered') {
      setState(() => _isLoadingSecret = false);
      return;
    }

    try {
      final orderId = widget.orderData['_id']['\$oid'];
      final secret = await _transactionService.getTotpSecret(orderId);
      if (mounted) {
        setState(() {
          _totpSecret = secret;
          _isLoadingSecret = false;
        });
        _generateAndSetCode(); // Generate the first code immediately
        // Start a timer to regenerate the code periodically
        _totpTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
          _generateAndSetCode();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingSecret = false;
          _displayCode = "Error";
        });
      }
    }
  }

  // --- REFACTORED: This method now uses the new 'otp' package ---
  void _generateAndSetCode() {
    if (_totpSecret == null || !mounted) return;

    final code = OTP.generateTOTPCodeString(
      _totpSecret!,
      DateTime.now().millisecondsSinceEpoch,
      interval: 60,
      algorithm:
          Algorithm.SHA1, // The new package requires specifying the algorithm
      isGoogle: true, // Ensures standard TOTP implementation
    );

    final formattedCode = '${code.substring(0, 3)}-${code.substring(3, 6)}';

    setState(() {
      _displayCode = formattedCode;
    });
  }

  Future<void> _handleConfirmReceipt() async {
    setState(() => _isConfirming = true);
    try {
      final orderId = widget.orderData['_id']['\$oid'];
      await _transactionService.confirmReceipt(orderId);
      widget.onUpdate(); // Refresh the orders list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() => _isConfirming = false);
      }
    }
  }

  Future<void> _handleUploadProof() async {
    final XFile? image = await _picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 70,
    );
    if (image == null) return;

    setState(() => _isUploadingProof = true);
    try {
      final orderId = widget.orderData['_id']['\$oid'];
      await _transactionService.uploadBuyerProof(orderId, image);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Proof uploaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
      widget.onUpdate(); // Refresh to show the new state if needed
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() => _isUploadingProof = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final status = widget.orderData['status'] ?? 'pending';
    final hasBuyerProof = widget.orderData['buyer_proof_url'] != null;
    final double totalAmount =
        (widget.orderData['total_amount'] as num?)?.toDouble() ?? 0.0;
    final String orderDate = TimeAgoService.format(
      widget.orderData['created_at']?['\$date'],
    );
    final List<dynamic> items =
        widget.orderData['items'] as List<dynamic>? ?? [];
    final String? proofImageUrl =
        widget.orderData['proof_of_delivery_url'] as String?;

    Color statusColor;
    switch (status.toLowerCase()) {
      case 'paid':
        statusColor = Colors.blue;
        break;
      case 'delivered':
        statusColor = Colors.purple;
        break;
      case 'completed':
        statusColor = Colors.green;
        break;
      case 'pending':
        statusColor = Colors.orange;
        break;
      default:
        statusColor = Colors.grey;
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(height: 24, thickness: 2, color: Colors.black),
          // --- NEW: Message Seller Button ---
          GestureDetector(
            onTap: () {
              final sellerDetails =
                  widget.orderData['seller_details'] as Map<String, dynamic>? ??
                  {};
              final sellerName = sellerDetails['username'] ?? 'Seller';
              final orderId = widget.orderData['_id']['\$oid'];
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      ChatScreen(roomId: orderId, recipientName: sellerName),
                ),
              );
            },
            child: const NeuCard(
              margin: EdgeInsets.only(bottom: 16),
              padding: EdgeInsets.symmetric(vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(EvaIcons.messageSquareOutline, size: 20),
                  SizedBox(width: 8),
                  Text(
                    "Message Seller",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order #${widget.orderData['_id']['\$oid'].substring(0, 8)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                backgroundColor: statusColor,
                child: Text(
                  status.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const Divider(height: 24, thickness: 2, color: Colors.black),

          // --- NEW: Confirmation Code & Proof Section ---
          if (status == 'paid' || status == 'delivered')
            NeuCard(
              margin: const EdgeInsets.only(bottom: 16),
              backgroundColor: const Color(0xFFFEF7F0),
              child: Column(
                children: [
                  const ListTile(/* ... ListTile UI is the same */),
                  _isLoadingSecret
                      ? const CircularProgressIndicator()
                      : Text(
                          _displayCode, // Display the dynamic code
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 4,
                          ),
                        ),

                  GestureDetector(
                    onTap: _isUploadingProof ? null : _handleUploadProof,
                    child: NeuCard(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      backgroundColor: hasBuyerProof
                          ? Colors.green
                          : Colors.white,
                      child: Center(
                        child: _isUploadingProof
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    hasBuyerProof
                                        ? Icons.check_circle
                                        : Icons.camera_alt_outlined,
                                    color: hasBuyerProof
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    hasBuyerProof
                                        ? 'Proof Uploaded'
                                        : 'Add Photo Proof (Optional)',
                                    style: TextStyle(
                                      color: hasBuyerProof
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          ...items.map((item) => _buildOrderItem(item)),
          if (proofImageUrl != null) _buildProofOfDelivery(proofImageUrl),
          const Divider(height: 24, thickness: 2, color: Colors.black),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(orderDate, style: const TextStyle(color: Colors.grey)),
              Text(
                'Total: GHS ${totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          if (status == 'delivered')
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: GestureDetector(
                onTap: _isConfirming ? null : _handleConfirmReceipt,
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  backgroundColor: const Color(0xFF00D2D3),
                  child: Center(
                    child: _isConfirming
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'Confirm Receipt & Release Payment',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProofOfDelivery(String proofImagePath) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Proof of Delivery:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          FutureBuilder<String>(
            future: ConfigService.instance.getBaseUrl(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final fullUrl =
                    '${snapshot.data}/${proofImagePath.replaceAll('\\', '/')}';
                return NeuCard(
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.zero,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(13),
                    child: Image.network(fullUrl),
                  ),
                );
              }
              return const Center(child: CircularProgressIndicator());
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(Map<String, dynamic> item) {
    // This function remains the same as you provided
    final details = item['product_details'] as Map<String, dynamic>;
    final name = details['product_name'] ?? 'N/A';
    final quantity = item['quantity'] ?? 0;
    final price = (details['price'] as num?)?.toDouble() ?? 0.0;
    final List<dynamic> mediaList = details['media'] as List<dynamic>? ?? [];
    final String? imagePath = mediaList.isNotEmpty
        ? mediaList.first?.toString()
        : null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: imagePath != null
                  ? FutureBuilder<String>(
                      future: ConfigService.instance.getBaseUrl(),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          final fullUrl =
                              '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                          return Image.network(fullUrl, fit: BoxFit.cover);
                        }
                        return Container(color: Colors.grey.shade200);
                      },
                    )
                  : Container(
                      color: Colors.grey.shade200,
                      child: const Icon(Icons.image_not_supported),
                    ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text(
                  'Qty: $quantity',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
          Text('GHS ${(price * quantity).toStringAsFixed(2)}'),
        ],
      ),
    );
  }
}
