import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:wicker/services/booking_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
// Add these imports at the top
import 'package:wicker/screens/payment_webview_screen.dart';
import 'package:wicker/services/transaction_service.dart';

class MyAppointmentsScreen extends StatefulWidget {
  const MyAppointmentsScreen({super.key});

  @override
  State<MyAppointmentsScreen> createState() => _MyAppointmentsScreenState();
}

class _MyAppointmentsScreenState extends State<MyAppointmentsScreen> {
  final BookingService _bookingService = BookingService();
  final TransactionService _transactionService = TransactionService();
  late Future<List<Map<String, dynamic>>> _appointmentsFuture;

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  void _loadAppointments() {
    // Mark notifications as read, then fetch the list
    _bookingService.markBookingsAsViewed();
    setState(() {
      _appointmentsFuture = _bookingService.getMyAppointments();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: AppBar(title: const Text('My Appointments')),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _appointmentsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text("You have no appointments."));
          }

          final appointments = snapshot.data!;
          return ListView.builder(
            itemCount: appointments.length,
            itemBuilder: (context, index) {
              return _buildAppointmentCard(appointments[index]);
            },
          );
        },
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> booking) {
    final status = booking['status'];
    final businessName = booking['business_details']['business_name'];
    final serviceName = booking['service_details']['product_name'];
    final startTime = DateFormat.yMMMd().add_jm().format(
      DateTime.parse(booking['start_time']['\$date']),
    );

    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case 'paid': // <-- NEW STATUS
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'confirmed':
        statusColor = Colors.blue;
        statusIcon = Icons.check_circle_outline;
        break;
      case 'pending_confirmation':
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty_rounded;
        break;
      case 'declined':
        statusColor = Colors.red;
        statusIcon = Icons.cancel_outlined;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.info_outline;
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  serviceName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              NeuChip(
                label: status.replaceAll('_', ' ').toUpperCase(),
                icon: statusIcon,
                backgroundColor: statusColor,
              ),
            ],
          ),
          const Divider(height: 16),
          Text('With: $businessName'),
          Text('When: $startTime'),
          // --- REFACTORED: The "Pay to Secure" button logic ---
          if (status == 'confirmed')
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: GestureDetector(
                // In lib/screens/my_appointments_screen.dart

                // In the _buildAppointmentCard widget, find the GestureDetector for the "Pay" button
                // and replace its onTap method with this:
                onTap: () async {
                  try {
                    final serviceId =
                        booking['service_details']['_id']['\$oid'];
                    final bookingId = booking['_id']['\$oid'];

                    final authUrl = await _transactionService
                        .initiateTransaction(
                          productId: serviceId,
                          bookingId: bookingId,
                        );

                    if (!mounted) return;

                    final result = await Navigator.push<bool>(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            PaymentWebViewScreen(authorizationUrl: authUrl),
                      ),
                    );

                    // --- REFACTORED: Handle the result with a delay ---
                    if (result == true) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Payment successful! Securing your appointment...',
                            ),
                            backgroundColor: Colors.blue,
                          ),
                        );
                      }

                      // Wait for 2 seconds to allow the webhook to update the booking status
                      await Future.delayed(const Duration(seconds: 2));
                      _loadAppointments(); // Refresh the list
                    }
                    // --- End of REFACTOR ---
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(e.toString()),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },

                // onTap: () async {
                //   try {
                //     final serviceId =
                //         booking['service_details']['_id']['\$oid'];
                //     final bookingId = booking['_id']['\$oid'];

                //     final authUrl = await _transactionService
                //         .initiateTransaction(
                //           productId: serviceId,
                //           bookingId: bookingId,
                //         );

                //     if (!mounted) return;

                //     final result = await Navigator.push<bool>(
                //       context,
                //       MaterialPageRoute(
                //         builder: (context) =>
                //             PaymentWebViewScreen(authorizationUrl: authUrl),
                //       ),
                //     );

                //     // If payment was successful, refresh the list
                //     if (result == true) {
                //       _loadAppointments();
                //     }
                //   } catch (e) {
                //     if (mounted) {
                //       ScaffoldMessenger.of(context).showSnackBar(
                //         SnackBar(
                //           content: Text(e.toString()),
                //           backgroundColor: Colors.red,
                //         ),
                //       );
                //     }
                //   }
                // },
                child: const NeuCard(
                  backgroundColor: Color(0xFF00D2D3),
                  margin: EdgeInsets.zero,
                  child: Center(
                    child: Text(
                      'Pay to Secure Appointment',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
