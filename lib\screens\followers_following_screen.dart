import 'package:flutter/material.dart';
import 'package:wicker/models/user.dart';

class FollowersFollowingScreen extends StatelessWidget {
  final String title;
  final Future<List<User>> usersFuture;

  const FollowersFollowingScreen({super.key, required this.title, required this.usersFuture});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
      ),
      body: FutureBuilder<List<User>>(
        future: usersFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No users found.'));
          }

          final users = snapshot.data!;

          return ListView.builder(
            itemCount: users.length,
            itemBuilder: (context, index) {
              final user = users[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundImage: user.profilePicUrl != null
                      ? NetworkImage(user.profilePicUrl!)
                      : null,
                  child: user.profilePicUrl == null
                      ? const Icon(Icons.person)
                      : null,
                ),
                title: Text(user.username),
              );
            },
          );
        },
      ),
    );
  }
}
