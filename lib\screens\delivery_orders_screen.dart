import 'package:flutter/material.dart';
import 'package:wicker/models/delivery_order.dart';
import 'package:wicker/services/delivery_service.dart';
import 'package:wicker/widgets/delivery_order_card.dart';

class DeliveryOrdersScreen extends StatefulWidget {
  const DeliveryOrdersScreen({super.key});

  @override
  _DeliveryOrdersScreenState createState() => _DeliveryOrdersScreenState();
}

class _DeliveryOrdersScreenState extends State<DeliveryOrdersScreen> {
  final DeliveryService _deliveryService = DeliveryService();
  late Future<List<DeliveryOrder>> _deliveryOrdersFuture;

  @override
  void initState() {
    super.initState();
    _deliveryOrdersFuture = _deliveryService.getDeliveryOrders();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Deliveries'),
      ),
      body: FutureBuilder<List<DeliveryOrder>>(
        future: _deliveryOrdersFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No delivery orders found.'));
          }

          final deliveryOrders = snapshot.data!;
          return ListView.builder(
            itemCount: deliveryOrders.length,
            itemBuilder: (context, index) {
              final order = deliveryOrders[index];
              return DeliveryOrderCard(order: order);
            },
          );
        },
      ),
    );
  }
}
