import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:wicker/services/booking_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:date_picker_timeline/date_picker_timeline.dart';
import 'package:table_calendar/table_calendar.dart';

class MyScheduleScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const MyScheduleScreen({super.key, required this.businessData});

  @override
  State<MyScheduleScreen> createState() => _MyScheduleScreenState();
}

class _MyScheduleScreenState extends State<MyScheduleScreen> {
  final BookingService _bookingService = BookingService();

  List<Map<String, dynamic>> _allBookings = [];
  List<Map<String, dynamic>> _filteredBookings = [];
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = true;

  Future<String>? _briefingFuture;

  @override
  void initState() {
    super.initState();
    _fetchSchedule();
  }

  Future<void> _fetchSchedule() async {
    setState(() {
      _isLoading = true;
      _briefingFuture = null;
    });
    try {
      final schedule = await _bookingService.getMySchedule(
        widget.businessData['_id']['\$oid'],
      );
      setState(() {
        _allBookings = schedule;
        _filterAndFetchBriefingForSelectedDate();
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching schedule: ${e.toString()}')),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterAndFetchBriefingForSelectedDate() {
    final startOfDay = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
    );
    final endOfDay = startOfDay.add(const Duration(days: 1));

    _filteredBookings = _allBookings.where((booking) {
      final startTime = DateTime.parse(booking['start_time']['\$date']);
      return startTime.isAfter(startOfDay) && startTime.isBefore(endOfDay);
    }).toList();

    if (isSameDay(_selectedDate, DateTime.now())) {
      _briefingFuture = _bookingService.getDailyBriefing(_filteredBookings);
    } else {
      _briefingFuture = null;
    }
  }

  Future<void> _handleConfirm(String bookingId) async {
    try {
      await _bookingService.confirmBooking(bookingId);
      await _fetchSchedule(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    }
  }

  Future<void> _handleDecline(String bookingId) async {
    try {
      await _bookingService.declineBooking(bookingId);
      await _fetchSchedule(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    }
  }

  Future<void> _handleSwipeAction(String bookingId, String action) async {
    try {
      if (action == 'complete') {
        await _bookingService.completeBooking(bookingId);
      } else {
        await _bookingService.markAsNoShow(bookingId);
      }
      await _fetchSchedule(); // Refresh list after action
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: AppBar(
        title: Text('${widget.businessData['business_name']}\'s Schedule'),
      ),
      body: Column(
        children: [
          _buildDatePicker(),
          _buildAiBriefingCard(),
          if (_isLoading)
            const Expanded(child: Center(child: CircularProgressIndicator()))
          else if (_filteredBookings.isEmpty)
            Expanded(
              child: Center(
                child: Text(
                  "No appointments for ${DateFormat.yMMMd().format(_selectedDate)}.",
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _filteredBookings.length,
                itemBuilder: (context, index) {
                  return _buildBookingTile(_filteredBookings[index]);
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDatePicker() {
    return NeuCard(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(8),
      child: DatePicker(
        DateTime.now(),
        height: 100,
        initialSelectedDate: _selectedDate,
        selectionColor: const Color(0xFF6C5CE7),
        selectedTextColor: Colors.white,
        onDateChange: (date) {
          setState(() {
            _selectedDate = date;
            _filterAndFetchBriefingForSelectedDate();
          });
        },
      ),
    );
  }

  Widget _buildAiBriefingCard() {
    if (_briefingFuture == null) {
      return const SizedBox.shrink();
    }
    return FutureBuilder<String>(
      future: _briefingFuture,
      builder: (context, snapshot) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 500),
          child: snapshot.hasData
              ? NeuCard(
                  key: const ValueKey('briefing_loaded'),
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  backgroundColor: const Color(0xFFFFE66D),
                  child: Row(
                    children: [
                      Icon(
                        EvaIcons.messageCircleOutline,
                        color: Colors.grey[800],
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          snapshot.data!,
                          style: TextStyle(color: Colors.grey[800]),
                        ),
                      ),
                    ],
                  ),
                )
              : const SizedBox.shrink(key: ValueKey('briefing_loading')),
        );
      },
    );
  }

  Widget _buildBookingTile(Map<String, dynamic> booking) {
    final bookingId = booking['_id']['\$oid'];
    final status = booking['status'];
    final clientName = booking['client_details']['username'];
    final serviceName = booking['service_details']['product_name'];
    final startTime = DateFormat.jm().format(
      DateTime.parse(booking['start_time']['\$date']),
    );

    Color statusColor;
    switch (status) {
      case 'confirmed':
        statusColor = Colors.blue;
        break;
      case 'pending_confirmation':
        statusColor = Colors.orange;
        break;
      case 'declined':
        statusColor = Colors.red;
        break;
      case 'completed':
        statusColor = Colors.green;
        break;
      case 'no_show':
        statusColor = Colors.grey.shade600;
        break;
      default:
        statusColor = Colors.grey;
    }

    final tileContent = NeuCard(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: Text(
              serviceName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text('With: $clientName'),
            trailing: NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              backgroundColor: statusColor,
              child: Text(
                status.replaceAll('_', ' ').toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 8.0),
            child: Text(
              "Time: $startTime",
              style: const TextStyle(color: Colors.black54),
            ),
          ),
          if (status == 'pending_confirmation')
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () => _handleDecline(bookingId),
                    child: const NeuCard(
                      margin: EdgeInsets.only(right: 8),
                      backgroundColor: Color(0xFFFF6B6B),
                      padding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Text(
                        'Decline',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => _handleConfirm(bookingId),
                    child: const NeuCard(
                      margin: EdgeInsets.zero,
                      backgroundColor: Color(0xFF4ECDC4),
                      padding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Text(
                        'Confirm',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );

    if (status == 'confirmed') {
      return Dismissible(
        key: Key(bookingId),
        onDismissed: (direction) {
          final action = direction == DismissDirection.startToEnd
              ? 'complete'
              : 'no_show';
          _handleSwipeAction(bookingId, action);
        },
        background: _buildSwipeBackground(
          Colors.green,
          "Completed",
          EvaIcons.checkmark,
          Alignment.centerLeft,
        ),
        secondaryBackground: _buildSwipeBackground(
          Colors.red,
          "No-Show",
          EvaIcons.close,
          Alignment.centerRight,
        ),
        child: tileContent,
      );
    }

    return tileContent;
  }

  Widget _buildSwipeBackground(
    Color color,
    String label,
    IconData icon,
    AlignmentGeometry alignment,
  ) {
    return NeuCard(
      margin: const EdgeInsets.symmetric(vertical: 8),
      backgroundColor: color,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Align(
          alignment: alignment,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (alignment == Alignment.centerLeft) ...[
                Icon(icon, color: Colors.white),
                const SizedBox(width: 8),
              ],
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (alignment == Alignment.centerRight) ...[
                const SizedBox(width: 8),
                Icon(icon, color: Colors.white),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
