import 'package:flutter/material.dart';
import 'package:wicker/models/delivery_order.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class DeliveryOrderCard extends StatelessWidget {
  final DeliveryOrder order;

  const DeliveryOrderCard({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: NeuCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order ID: ${order.id}', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('Item: ${order.itemDetails["description"]}'),
            const SizedBox(height: 8),
            Text('Pickup: ${order.pickupLocation["text"]}'),
            const SizedBox(height: 8),
            Text('Status: ${order.status}', style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
      ),
    );
  }
}
