class DeliveryOrder {
  final String id;
  final String senderId;
  final String recipientId;
  final String recipientAddressId;
  final Map<String, dynamic> pickupLocation;
  final Map<String, dynamic> itemDetails;
  final String status;
  final String? deliveryProviderId;
  final DateTime createdAt;
  final DateTime updatedAt;

  DeliveryOrder({
    required this.id,
    required this.senderId,
    required this.recipientId,
    required this.recipientAddressId,
    required this.pickupLocation,
    required this.itemDetails,
    required this.status,
    this.deliveryProviderId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DeliveryOrder.fromJson(Map<String, dynamic> json) {
    return DeliveryOrder(
      id: json['_id']['\$oid'],
      senderId: json['sender_id']['\$oid'],
      recipientId: json['recipient_id']['\$oid'],
      recipientAddressId: json['recipient_address_id']['\$oid'],
      pickupLocation: json['pickup_location'],
      itemDetails: json['item_details'],
      status: json['status'],
      deliveryProviderId: json['delivery_provider_id'] != null ? json['delivery_provider_id']['\$oid'] : null,
      createdAt: DateTime.parse(json['created_at']['\$date']),
      updatedAt: DateTime.parse(json['updated_at']['\$date']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': {'\$oid': id},
      'sender_id': {'\$oid': senderId},
      'recipient_id': {'\$oid': recipientId},
      'recipient_address_id': {'\$oid': recipientAddressId},
      'pickup_location': pickupLocation,
      'item_details': itemDetails,
      'status': status,
      'delivery_provider_id': deliveryProviderId != null ? {'\$oid': deliveryProviderId} : null,
      'created_at': {'\$date': createdAt.toIso8601String()},
      'updated_at': {'\$date': updatedAt.toIso8601String()},
    };
  }
}
