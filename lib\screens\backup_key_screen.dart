import 'package:flutter/material.dart';
import 'package:wicker/services/crypto_service.dart';
import 'package:wicker/screens/confirm_backup_screen.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class BackupKeyScreen extends StatefulWidget {
  const BackupKeyScreen({super.key});

  @override
  _BackupKeyScreenState createState() => _BackupKeyScreenState();
}

class _BackupKeyScreenState extends State<BackupKeyScreen> {
  final CryptoService _cryptoService = CryptoService();
  late Future<String?> _mnemonicFuture;

  @override
  void initState() {
    super.initState();
    _mnemonicFuture = _cryptoService.getMnemonic();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backup Your Key'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: FutureBuilder<String?>(
          future: _mnemonicFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }
            if (snapshot.hasError || !snapshot.hasData) {
              return const Center(child: Text('Could not load mnemonic phrase.'));
            }

            final mnemonic = snapshot.data!;
            final words = mnemonic.split(' ');

            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Please write down this 12-word phrase and keep it in a safe place. You will need it to recover your account.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 32),
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  alignment: WrapAlignment.center,
                  children: words.map((word) {
                    return NeuChip(
                      label: word,
                      icon: Icons.vpn_key,
                    );
                  }).toList(),
                ),
                const Spacer(),
                NeuButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ConfirmBackupScreen(mnemonic: mnemonic),
                      ),
                    );
                  },
                  child: const Text('I have written it down'),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
