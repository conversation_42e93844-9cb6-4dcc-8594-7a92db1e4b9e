import 'package:wicker/models/user.dart';
import 'package:wicker/models/place.dart';

class BusinessClaim {
  final String id;
  final User user;
  final Place place;
  final String documentPath;
  final String status;
  final DateTime createdAt;

  BusinessClaim({
    required this.id,
    required this.user,
    required this.place,
    required this.documentPath,
    required this.status,
    required this.createdAt,
  });

  factory BusinessClaim.fromJson(Map<String, dynamic> json) {
    return BusinessClaim(
      id: json['_id']['_id'],
      user: User.from<PERSON>son(json['user_details'] ?? {}),
      place: Place.fromJson(json['place_details'] ?? {}),
      documentPath: json['document_path'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']['_date']),
    );
  }
}
