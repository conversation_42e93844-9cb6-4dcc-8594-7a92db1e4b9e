from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required
from bson import json_util, ObjectId
import re
import json
import datetime

search_bp = Blueprint('search_bp', __name__)

# This remains as a reference for the AI model
SEARCHABLE_COLLECTIONS = {
    "businesses": ["business_name", "description"],
    "products": ["product_name", "description", "price", "product_type", "product_sub_type"],
    "places": ["name", "category", "location"],
    "posts": ["text_content"],
    "users": ["username"],
}

@search_bp.route('/', methods=['POST'])
@jwt_required()
def ai_powered_search():
    """
    Uses an AI agent to interpret a natural language query, convert it
    into a structured database query, execute it, and return the
    results along with a natural language summary.
    """
    db = search_bp.db
    openai_client = current_app.openai_client
    data = request.get_json()
    query = data.get('query')
    # In a real app, you'd get the user's location from the request
    # For now, we'll use a default location in Accra.
    user_location = data.get('location', {"lat": 5.6037, "lon": -0.1870})


    if not query:
        return jsonify({"msg": "Query is required"}), 400

    # --- AI Agent Prompt Engineering ---
    prompt = f"""
    You are a highly intelligent search assistant for a local discovery app in Accra, Ghana.
    Your task is to analyze a user's natural language query and convert it into a structured MongoDB query.
    The current date is {datetime.datetime.now().strftime('%Y-%m-%d')}.

    Here are the collections and their searchable fields:
    - businesses: ["business_name", "description"] (e.g., shops, companies)
    - products: ["product_name", "description", "price", "product_type", "product_sub_type"] (e.g., items for sale)
    - places: ["name", "category", "location"] (e.g., restaurants, cafes, markets with geo-coordinates)
    - posts: ["text_content"] (user-generated content)
    - users: ["username"]

    Analyze the following user query: "{query}"

    Based on the query, perform these actions:
    1.  **Generate a concise, friendly, one-sentence summary** of what you are searching for. This will be shown to the user.
    2.  **Determine the primary search `keyword`**. This should be the main noun or service (e.g., "plumber", "waakye", "coffee shop"). If the query is mainly about location (e.g., "cafes near me"), the keyword should be the place type ("cafe").
    3.  **Identify any `filters`**. Extract constraints like price, category, or type.
        - Example for price: "less than GHC50" -> {{"price": {{"$lt": 50}}}}
        - Example for category: "restaurants near me" -> {{"category": "Restaurant"}}
    4.  **Detect `geospatial` intent**. If the query includes "near me", "around", "close by", or a specific location, set this to true.
    5.  **Identify the target `collections`** to search. Choose from the list above.

    Your response MUST be a valid JSON object with the following structure:
    {{
      "summary": "<Your one-sentence summary>",
      "keyword": "<primary_keyword>",
      "filters": {{}},
      "geospatial": boolean,
      "collections": ["<collection1>", "<collection2>"]
    }}
    """

    try:
        # --- Call OpenAI API ---
        completion = openai_client.chat.completions.create(
            model="gpt-4-turbo",
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": "You are a search assistant that provides responses in JSON format."},
                {"role": "user", "content": prompt}
            ]
        )
        ai_response = json.loads(completion.choices[0].message.content)
        
        # --- Construct and Execute Database Query from AI Response ---
        search_keyword = ai_response.get("keyword", "")
        search_collections = ai_response.get("collections", [])
        search_filters = ai_response.get("filters", {})
        is_geospatial = ai_response.get("geospatial", False)
        
        all_results = []
        
        for collection_name in search_collections:
            if collection_name not in SEARCHABLE_COLLECTIONS:
                continue

            results = []
            # --- REFACTORED LOGIC: Handle geospatial queries differently ---
            if collection_name == 'places' and is_geospatial:
                print("Executing geospatial query...")
                # Use an aggregation pipeline for geospatial search
                pipeline = [
                    {
                        '$geoNear': {
                            'near': {
                                'type': 'Point',
                                'coordinates': [user_location['lon'], user_location['lat']]
                            },
                            'distanceField': 'distance', # Outputs the distance in meters
                            'maxDistance': 10000,       # Search within a 10km radius
                            'query': search_filters,    # Apply other filters like category
                            'spherical': True
                        }
                    }
                ]
                results = list(db[collection_name].aggregate(pipeline))
            else:
                # Use text search for all other cases
                print(f"Executing text search on '{collection_name}'...")
                mongo_query = {'$text': {'$search': search_keyword}}
                if search_filters:
                    mongo_query.update(search_filters)
                results = list(db[collection_name].find(mongo_query))

            for result in results:
                result['collection'] = collection_name
            all_results.extend(results)

        return jsonify({
            "summary": ai_response.get("summary", f"Found {len(all_results)} results for '{query}'"),
            "results": json.loads(json_util.dumps(all_results))
        }), 200

    except Exception as e:
        print(f"An unexpected error occurred during AI-powered search: {e}")
        return regex_fallback_search(db, query)

def regex_fallback_search(db, query):
    """A simple regex search used as a fallback if the AI agent fails."""
    all_results = []
    regex = re.compile(query, re.IGNORECASE)
    for collection_name, fields in SEARCHABLE_COLLECTIONS.items():
        try:
            or_conditions = [{field: regex} for field in fields]
            results = list(db[collection_name].find({"$or": or_conditions}))
            for result in results:
                result['collection'] = collection_name
            all_results.extend(results)
        except Exception as e:
            print(f"Error during fallback search in {collection_name}: {e}")
            pass
    
    return jsonify({
        "summary": f"Found {len(all_results)} results (fallback search)",
        "results": json.loads(json_util.dumps(all_results))
    }), 200


@search_bp.route('/instant', methods=['POST'])
@jwt_required()
def instant_search():
    db = search_bp.db
    data = request.get_json()
    query = data.get('query')
    if not query:
        return jsonify({"msg": "Query is required"}), 400

    return regex_fallback_search(db, query)