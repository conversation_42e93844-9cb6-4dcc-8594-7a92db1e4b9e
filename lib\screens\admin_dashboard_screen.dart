import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

import 'package:wicker/screens/verify_doctors_screen.dart';
import 'package:wicker/screens/verify_delivery_providers_screen.dart';
import 'package:wicker/screens/verify_business_claims_screen.dart';

class AdminDashboardScreen extends StatelessWidget {
  const AdminDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            NeuButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VerifyDoctorsScreen(),
                  ),
                );
              },
              child: const Text('Verify Doctors'),
            ),
            const SizedBox(height: 16),
            NeuButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VerifyDeliveryProvidersScreen(),
                  ),
                );
              },
              child: const Text('Verify Delivery Providers'),
            ),
            const SizedBox(height: 16),
            NeuButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VerifyBusinessClaimsScreen(),
                  ),
                );
              },
              child: const Text('Verify Business Claims'),
            ),
          ],
        ),
      ),
    );
  }
}
