class DeliveryProvider {
  final String id;
  final String userId;
  final Map<String, dynamic> vehicleDetails;
  final String availability;
  final String verificationStatus;

  DeliveryProvider({
    required this.id,
    required this.userId,
    required this.vehicleDetails,
    required this.availability,
    required this.verificationStatus,
  });

  factory DeliveryProvider.fromJson(Map<String, dynamic> json) {
    return DeliveryProvider(
      id: json['_id']['_oid'],
      userId: json['user_id']['_oid'],
      vehicleDetails: json['vehicle_details'],
      availability: json['availability'],
      verificationStatus: json['verification_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': {'_oid': id},
      'user_id': {'_oid': userId},
      'vehicle_details': vehicleDetails,
      'availability': availability,
      'verification_status': verificationStatus,
    };
  }
}
