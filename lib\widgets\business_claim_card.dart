import 'package:flutter/material.dart';
import 'package:wicker/models/business_claim.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/services/config_service.dart';
import 'package:url_launcher/url_launcher.dart';

class BusinessClaimCard extends StatelessWidget {
  final BusinessClaim claim;
  final Function(String, bool) onVerify;

  const BusinessClaimCard({super.key, required this.claim, required this.onVerify});

  void _launchDocumentUrl(BuildContext context) async {
    final baseUrl = await ConfigService.instance.getBaseUrl();
    final documentUrl = '$baseUrl/${claim.documentPath.replaceAll('\\', '/')}';
    if (await canLaunch(documentUrl)) {
      await launch(documentUrl);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open document.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: NeuCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Claim ID: ${claim.id}', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('User: ${claim.user.username}'),
            const SizedBox(height: 8),
            Text('Place: ${claim.place.name}'),
            const SizedBox(height: 16),
            NeuButton(
              onPressed: () => _launchDocumentUrl(context),
              child: const Text('View Document'),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                NeuButton(
                  onPressed: () => onVerify(claim.id, true),
                  child: const Text('Approve'),
                  backgroundColor: Colors.green,
                ),
                const SizedBox(width: 8),
                NeuButton(
                  onPressed: () => onVerify(claim.id, false),
                  child: const Text('Reject'),
                  backgroundColor: Colors.red,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
