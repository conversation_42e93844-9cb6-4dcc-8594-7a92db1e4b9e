class Prescription {
  final String id;
  final String doctorId;
  final String patientId;
  final String medication;
  final String dosage;
  final String instructions;
  final DateTime createdAt;

  Prescription({
    required this.id,
    required this.doctorId,
    required this.patientId,
    required this.medication,
    required this.dosage,
    required this.instructions,
    required this.createdAt,
  });

  factory Prescription.fromJson(Map<String, dynamic> json) {
    return Prescription(
      id: json['_id']['\$oid'],
      doctorId: json['doctor_id']['\$oid'],
      patientId: json['patient_id']['\$oid'],
      medication: json['medication'],
      dosage: json['dosage'],
      instructions: json['instructions'],
      createdAt: DateTime.parse(json['created_at']['\$date']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': {'\$oid': id},
      'doctor_id': {'\$oid': doctorId},
      'patient_id': {'\$oid': patientId},
      'medication': medication,
      'dosage': dosage,
      'instructions': instructions,
      'created_at': {'\$date': createdAt.toIso8601String()},
    };
  }
}
