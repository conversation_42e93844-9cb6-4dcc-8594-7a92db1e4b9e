// import 'package:eva_icons_flutter/eva_icons_flutter.dart';
// import 'package:flutter/material.dart';
// import 'package:wicker/screens/create_business_screen.dart';
// import 'package:wicker/screens/inventory_management_screen.dart';
// import 'package:wicker/services/config_service.dart';
// import 'package:wicker/services/ecommerce_service.dart';
// import 'package:wicker/widgets/neubrutalist_widgets.dart';

// class MyBusinessesScreen extends StatefulWidget {
//   const MyBusinessesScreen({super.key});

//   @override
//   State<MyBusinessesScreen> createState() => _MyBusinessesScreenState();
// }

// class _MyBusinessesScreenState extends State<MyBusinessesScreen> {
//   final EcommerceService _ecommerceService = EcommerceService();
//   late Future<List<Map<String, dynamic>>> _businessesFuture;

//   @override
//   void initState() {
//     super.initState();
//     _businessesFuture = _ecommerceService.getMyBusinesses();
//   }

//   void _refreshBusinesses() {
//     setState(() {
//       _businessesFuture = _ecommerceService.getMyBusinesses();
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(60.0),
//         child: AppBar(
//           backgroundColor: Colors.white,
//           elevation: 0,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back, color: Colors.black),
//             onPressed: () => Navigator.of(context).pop(),
//           ),
//           title: const Text(
//             'My Businesses',
//             style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//           ),
//           bottom: PreferredSize(
//             preferredSize: const Size.fromHeight(4.0),
//             child: Container(color: Colors.black, height: 3.0),
//           ),
//         ),
//       ),
//       body: FutureBuilder<List<Map<String, dynamic>>>(
//         future: _businessesFuture,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           }
//           if (snapshot.hasError) {
//             return Center(child: Text('Error: ${snapshot.error}'));
//           }
//           if (!snapshot.hasData || snapshot.data!.isEmpty) {
//             return const Center(
//               child: Text("You haven't created any businesses yet."),
//             );
//           }

//           final businesses = snapshot.data!;
//           return ListView.builder(
//             itemCount: businesses.length,
//             itemBuilder: (context, index) {
//               return _buildBusinessTile(businesses[index]);
//             },
//           );
//         },
//       ),
//       floatingActionButton: FloatingActionButton.extended(
//         onPressed: () async {
//           final result = await Navigator.push<bool>(
//             context,
//             MaterialPageRoute(
//               builder: (context) => const CreateBusinessScreen(),
//             ),
//           );
//           if (result == true) {
//             _refreshBusinesses();
//           }
//         },
//         label: const Text('New Business'),
//         icon: const Icon(EvaIcons.plus),
//         backgroundColor: const Color(0xFF00D2D3),
//       ),
//     );
//   }

//   Widget _buildBusinessTile(Map<String, dynamic> businessData) {
//     final List<dynamic> images = businessData['images'] as List<dynamic>? ?? [];
//     final String? imagePath = images.isNotEmpty
//         ? images.first.toString()
//         : null;

//     return GestureDetector(
//       onTap: () {
//         Navigator.push(
//           context,
//           MaterialPageRoute(
//             builder: (context) =>
//                 InventoryManagementScreen(businessData: businessData),
//           ),
//         );
//       },
//       child: NeuCard(
//         margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//         child: Row(
//           children: [
//             NeuCard(
//               margin: EdgeInsets.zero,
//               padding: EdgeInsets.zero,
//               child: ClipRRect(
//                 borderRadius: BorderRadius.circular(13),
//                 child: SizedBox(
//                   width: 70,
//                   height: 70,
//                   child: imagePath != null
//                       ? FutureBuilder<String>(
//                           future: ConfigService.instance.getBaseUrl(),
//                           builder: (context, snapshot) {
//                             if (snapshot.hasData) {
//                               final fullUrl =
//                                   '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
//                               return Image.network(fullUrl, fit: BoxFit.cover);
//                             }
//                             return const Center(
//                               child: CircularProgressIndicator(),
//                             );
//                           },
//                         )
//                       : Container(
//                           color: Colors.grey.shade200,
//                           child: const Icon(
//                             Icons.storefront,
//                             color: Colors.grey,
//                           ),
//                         ),
//                 ),
//               ),
//             ),
//             const SizedBox(width: 16),
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     businessData['business_name'] ?? 'Untitled Business',
//                     style: const TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 4),
//                   const Text(
//                     "Manage Inventory & Sales",
//                     style: TextStyle(fontSize: 14, color: Colors.grey),
//                   ),
//                 ],
//               ),
//             ),
//             const Icon(EvaIcons.arrowIosForwardOutline, color: Colors.grey),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:wicker/screens/create_business_screen.dart';
import 'package:wicker/screens/inventory_management_screen.dart';
import 'package:wicker/screens/my_schedule_screen.dart';
import 'package:wicker/screens/payout_settings_screen.dart';
import 'package:wicker/screens/set_availability_screen.dart';
import 'package:wicker/screens/set_location_screen.dart'; // <-- ADD THIS IMPORT
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class MyBusinessesScreen extends StatefulWidget {
  const MyBusinessesScreen({super.key});

  @override
  State<MyBusinessesScreen> createState() => _MyBusinessesScreenState();
}

class _MyBusinessesScreenState extends State<MyBusinessesScreen> {
  final EcommerceService _ecommerceService = EcommerceService();
  late Future<List<Map<String, dynamic>>> _businessesFuture;

  @override
  void initState() {
    super.initState();
    _businessesFuture = _ecommerceService.getMyBusinesses();
  }

  void _refreshBusinesses() {
    setState(() {
      _businessesFuture = _ecommerceService.getMyBusinesses();
    });
  }

  // --- NEW: Method to handle setting the pickup location ---
  Future<void> _handleSetLocation(String businessId) async {
    final LatLng? result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SetLocationScreen()),
    );

    if (result != null && mounted) {
      try {
        await _ecommerceService.setPickupLocation(
          businessId: businessId,
          location: result,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Pickup location saved!'),
            backgroundColor: Colors.green,
          ),
        );
        _refreshBusinesses(); // Refresh the list to hide the prompt
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(e.toString()), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'My Businesses',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.credit_card, color: Colors.black),
              tooltip: 'Payout Settings',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PayoutSettingsScreen(),
                  ),
                );
              },
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _businessesFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(
              child: Text("You haven't created any businesses yet."),
            );
          }

          final businesses = snapshot.data!;
          return ListView.builder(
            padding: const EdgeInsets.only(top: 8, bottom: 80),
            itemCount: businesses.length,
            itemBuilder: (context, index) {
              return _buildBusinessTile(businesses[index]);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBusinessScreen(),
            ),
          );
          if (result == true) {
            _refreshBusinesses();
          }
        },
        label: const Text('New Business'),
        icon: const Icon(EvaIcons.plus),
        backgroundColor: const Color(0xFF00D2D3),
      ),
    );
  }

  Widget _buildBusinessTile(Map<String, dynamic> businessData) {
    final List<dynamic> images = businessData['images'] as List<dynamic>? ?? [];
    final String? imagePath = images.isNotEmpty
        ? images.first.toString()
        : null;

    // Check if the business needs a location set
    final bool isLocationless =
        businessData['linked_place_id'] == null &&
        businessData['pickup_location'] == null;

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: SizedBox(
                    width: 70,
                    height: 70,
                    child: imagePath != null
                        ? FutureBuilder<String>(
                            future: ConfigService.instance.getBaseUrl(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                final fullUrl =
                                    '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                                return Image.network(
                                  fullUrl,
                                  fit: BoxFit.cover,
                                );
                              }
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            },
                          )
                        : Container(
                            color: Colors.grey.shade200,
                            child: const Icon(
                              Icons.storefront,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      businessData['business_name'] ?? 'Untitled Business',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      businessData['description'] ?? "No description",
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          // --- NEW: Conditional prompt for locationless businesses ---
          if (isLocationless)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: NeuCard(
                margin: EdgeInsets.zero,
                backgroundColor: Colors.orange.shade100,
                borderColor: Colors.orange,
                child: Column(
                  children: [
                    const ListTile(
                      leading: Icon(
                        Icons.pin_drop_outlined,
                        color: Colors.orange,
                      ),
                      title: Text(
                        'Action Required',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(
                        'Set a pickup location to enable deliveries for this business.',
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: GestureDetector(
                        onTap: () =>
                            _handleSetLocation(businessData['_id']['\$oid']),
                        child: const NeuCard(
                          margin: EdgeInsets.zero,
                          backgroundColor: Colors.orange,
                          child: Center(
                            child: Text(
                              'Set Pickup Location',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            SetAvailabilityScreen(businessData: businessData),
                      ),
                    );
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.only(right: 4),
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Center(
                      child: Text(
                        "Hours",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
              // --- NEW: Manage Schedule Button ---
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            MyScheduleScreen(businessData: businessData),
                      ),
                    );
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.symmetric(horizontal: 4),
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Center(
                      child: Text(
                        "Schedule",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
              // --- End of NEW ---
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => InventoryManagementScreen(
                          businessData: businessData,
                        ),
                      ),
                    );
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.only(left: 4),
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Center(
                      child: Text(
                        "Inventory",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
