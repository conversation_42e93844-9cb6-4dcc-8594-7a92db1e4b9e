import 'dart:async';
import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:provider/provider.dart';
import 'package:wicker/screens/admin_dashboard_screen.dart';
import 'package:wicker/screens/apply_for_role_screen.dart';
import 'package:wicker/screens/address_book_screen.dart';
import 'package:wicker/screens/create_delivery_order_screen.dart';
import 'package:wicker/screens/create_medical_record_screen.dart';
import 'package:wicker/screens/create_prescription_screen.dart';
import 'package:wicker/screens/delivery_orders_screen.dart';
import 'package:wicker/screens/edit_profile_screen.dart';
import 'package:wicker/screens/followers_following_screen.dart';
import 'package:wicker/screens/full_screen_image_screen.dart';
import 'package:wicker/screens/medical_records_screen.dart';
import 'package:wicker/screens/my_appointments_screen.dart';
import 'package:wicker/screens/my_businesses_screen.dart';
import 'package:wicker/screens/orders_screen.dart';
import 'package:wicker/screens/prescriptions_screen.dart';
import 'package:wicker/screens/summarize_consultation_screen.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/booking_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/notification_service.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/glowing_border_wrapper.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/user_contributions_tab.dart';
import 'package:wicker/widgets/user_playlists_tab.dart';

class HubScreen extends StatefulWidget {
  final String? userId;
  const HubScreen({super.key, this.userId});

  @override
  _HubScreenState createState() => _HubScreenState();
}

class _HubScreenState extends State<HubScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();
  final ConfigService _configService = ConfigService.instance;
  final BookingService _bookingService = BookingService();
  Timer? _notificationTimer;
  List<String> _userRoles = [];
  late Future<Map<String, dynamic>> _profileFuture;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeProfile();
    _startNotificationPolling();
  }

  void _initializeProfile() async {
    final token = await _authService.getAccessToken();
    if (token != null) {
      _currentUserId = _authService.getUserIdFromToken(token);
      _userRoles = await _authService.getRoles();
    }
    setState(() {
      _profileFuture = _fetchProfileData();
    });
  }

  void _startNotificationPolling() {
    _notificationTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        Provider.of<NotificationService>(context, listen: false)
            .fetchNotifications();
      }
    });
  }

  Future<Map<String, dynamic>> _fetchProfileData() {
    if (widget.userId != null && widget.userId != _currentUserId) {
      return _userService.getUserProfile(widget.userId!);
    } else {
      return _userService.getProfileHeader();
    }
  }

  void _refreshProfile() {
    setState(() {
      _profileFuture = _fetchProfileData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _notificationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _profileFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Could not load profile.'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _refreshProfile,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final profileData = snapshot.data!;
          final isOwnProfile =
              widget.userId == null || widget.userId == _currentUserId;

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  title: Text(
                    profileData['username'] ?? 'Profile',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  backgroundColor: Colors.white,
                  elevation: 0,
                  pinned: true,
                  actions: [
                    if (isOwnProfile)
                      IconButton(
                        icon: const Icon(
                          EvaIcons.settings2Outline,
                          color: Colors.black,
                        ),
                        onPressed: () {},
                      ),
                  ],
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(4.0),
                    child: Container(color: Colors.black, height: 3.0),
                  ),
                ),
                SliverToBoxAdapter(
                  child: _buildProfileHeader(profileData, isOwnProfile),
                ),
                SliverPersistentHeader(
                  delegate: _SliverAppBarDelegate(
                    TabBar(
                      controller: _tabController,
                      indicator: const BoxDecoration(
                        color: Color(0xFFFFE66D),
                        border: Border(
                          bottom: BorderSide(color: Colors.black, width: 3.0),
                        ),
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelColor: Colors.black,
                      unselectedLabelColor: Colors.grey.shade700,
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      tabs: const [
                        Tab(icon: Icon(EvaIcons.grid), text: 'Contributions'),
                        Tab(icon: Icon(EvaIcons.bookmark), text: 'Lists'),
                      ],
                    ),
                  ),
                  pinned: true,
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: const [
                UserContributionsTab(),
                UserPlaylistsTab(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(Map<String, dynamic> profile, bool isOwnProfile) {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        return FutureBuilder<String>(
          future: _configService.getBaseUrl(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const SizedBox(
                height: 250,
                child: Center(child: CircularProgressIndicator()),
              );
            }

            final baseUrl = snapshot.data!;
            String profilePicPath = profile['profile_pic_url']?.toString() ?? '';
            String profilePicUrl = profilePicPath.isNotEmpty
                ? '$baseUrl/${profilePicPath.replaceAll('\', '/')}'
                : 'https://i.pravatar.cc/150?u=${profile['_id']?['$oid']}' ;

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FullScreenImageScreen(
                                imageUrl: profilePicUrl,
                              ),
                            ),
                          );
                        },
                        child: NeuCard(
                          margin: EdgeInsets.zero,
                          padding: const EdgeInsets.all(4),
                          child: CircleAvatar(
                            radius: 60,
                            backgroundImage: NetworkImage(profilePicUrl),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: NeuCard(
                          margin: EdgeInsets.zero,
                          padding: const EdgeInsets.all(2),
                          child: GridView.count(
                            crossAxisCount: 2,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            mainAxisSpacing: 2,
                            crossAxisSpacing: 2,
                            childAspectRatio: 1.2,
                            children: [
                              if (isOwnProfile)
                                GestureDetector(
                                  onTap: () => ScaffoldMessenger.of(context)
                                      .showSnackBar(
                                    const SnackBar(
                                      content: Text('Coming soon!'),
                                    ),
                                  ),
                                  child: _buildStatQuad(
                                    "Balance",
                                    'GHS ${(profile['balance'] as num?)?.toDouble() ?? 0.0}',
                                    EvaIcons.creditCardOutline,
                                  ),
                                ),
                              GestureDetector(
                                onTap: () =>
                                    ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Coming soon!'),
                                  ),
                                ),
                                child: _buildStatQuad(
                                  "Points",
                                  (profile['points'] ?? 0).toString(),
                                  EvaIcons.starOutline,
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          FollowersFollowingScreen(
                                        title: 'Followers',
                                        usersFuture: _userService
                                            .getFollowers(profile['_id']['$oid']),
                                      ),
                                    ),
                                  );
                                },
                                child: _buildStatQuad(
                                  "Followers",
                                  (profile['followers_count'] ?? 0).toString(),
                                  EvaIcons.peopleOutline,
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          FollowersFollowingScreen(
                                        title: 'Following',
                                        usersFuture: _userService
                                            .getFollowing(profile['_id']['$oid']),
                                      ),
                                    ),
                                  );
                                },
                                child: _buildStatQuad(
                                  "Following",
                                  (profile['following_count'] ?? 0).toString(),
                                  EvaIcons.personDoneOutline,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    profile['username'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    profile['bio'] ?? 'No bio yet.',
                    style: TextStyle(color: Colors.grey[800], fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  _buildActionButtons(profile),
                  const SizedBox(height: 8),
                  if (isOwnProfile)
                    ..._buildUserActionList(notificationService),
                  if (isOwnProfile && _userRoles.contains('admin'))
                    ..._buildAdminTools(),
                  if (isOwnProfile && _userRoles.contains('doctor'))
                    ..._buildDoctorTools(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  List<Widget> _buildUserActionList(NotificationService notificationService) {
    return [
      _buildProfileListItem(
        icon: EvaIcons.shoppingBagOutline,
        text: "My Orders",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const OrdersScreen()),
        ),
        showGlow: notificationService.hasUnreadChats,
      ),
      _buildProfileListItem(
        icon: EvaIcons.calendarOutline,
        text: "My Appointments",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const MyAppointmentsScreen()),
        ),
        showGlow: notificationService.hasUnreadAppointments,
      ),
      _buildProfileListItem(
        icon: EvaIcons.bookOpenOutline,
        text: "Address Book",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AddressBookScreen()),
        ),
      ),
      _buildProfileListItem(
        icon: EvaIcons.carOutline,
        text: "New Delivery",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => const CreateDeliveryOrderScreen()),
        ),
      ),
      _buildProfileListItem(
        icon: EvaIcons.cubeOutline,
        text: "My Deliveries",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const DeliveryOrdersScreen()),
        ),
      ),
    ];
  }

  List<Widget> _buildAdminTools() {
    return [
      const SizedBox(height: 16),
      const Text("Admin Tools",
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
      const SizedBox(height: 8),
      _buildProfileListItem(
        icon: EvaIcons.shieldOutline,
        text: "Admin Dashboard",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AdminDashboardScreen()),
        ),
      ),
    ];
  }

  List<Widget> _buildDoctorTools() {
    return [
      const SizedBox(height: 16),
      const Text("Doctor Tools",
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
      const SizedBox(height: 8),
      _buildProfileListItem(
        icon: EvaIcons.fileTextOutline,
        text: "Medical Records",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const MedicalRecordsScreen()),
        ),
      ),
      _buildProfileListItem(
        icon: EvaIcons.fileAddOutline,
        text: "Create Medical Record",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => const CreateMedicalRecordScreen()),
        ),
      ),
      _buildProfileListItem(
        icon: EvaIcons.activityOutline,
        text: "Prescriptions",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const PrescriptionsScreen()),
        ),
      ),
      _buildProfileListItem(
        icon: EvaIcons.edit2Outline,
        text: "Create Prescription",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => const CreatePrescriptionScreen()),
        ),
      ),
      _buildProfileListItem(
        icon: EvaIcons.messageCircleOutline,
        text: "Summarize Consultation",
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => const SummarizeConsultationScreen()),
        ),
      ),
    ];
  }

  Widget _buildProfileListItem({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
    bool showGlow = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: GlowingBorderWrapper(
        showGlow: showGlow,
        borderWidth: 3.0,
        child: GestureDetector(
          onTap: onTap,
          child: NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF6C5CE7)),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    text,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                const Icon(EvaIcons.arrowIosForwardOutline, color: Colors.grey),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatQuad(String label, String value, IconData icon) {
    return NeuCard(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(4),
      backgroundColor: Colors.white,
      shadowOffset: 0,
      borderWidth: 1.5,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18, color: Colors.grey[800]),
          const SizedBox(height: 0.5),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
          Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 10)),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Map<String, dynamic> profileData) {
    final isOwnProfile =
        widget.userId == null || widget.userId == _currentUserId;

    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        if (isOwnProfile) {
          return Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    final result = await Navigator.push<bool>(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            EditProfileScreen(profileData: profileData),
                      ),
                    );
                    if (result == true) {
                      _refreshProfile();
                    }
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    child: Center(
                      child: Text(
                        "Edit Profile",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GlowingBorderWrapper(
                  showGlow: notificationService.hasPendingBookings,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MyBusinessesScreen(),
                        ),
                      );
                    },
                    child: const NeuCard(
                      margin: EdgeInsets.zero,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: Color(0xFF00D2D3),
                      child: Center(
                        child: Text(
                          "My Businesses",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ApplyForRoleScreen(),
                      ),
                    );
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    backgroundColor: Color(0xFF48dbfb),
                    child: Center(
                      child: Text(
                        "Apply for Role",
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          return Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {},
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    backgroundColor: Color(0xFF6C5CE7),
                    child: Center(
                      child: Text(
                        "Follow",
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () {},
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    child: Center(
                      child: Text(
                        "Message",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        }
      },
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: const Color(0xFFFEF7F0),
      child: NeuCard(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
        padding: EdgeInsets.zero,
        child: _tabBar,
      ),
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}