import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/screens/place_detail_screen.dart';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/services/share_service.dart';
import 'package:wicker/widgets/add_to_playlist_modal.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class PlaceDetailCard extends StatefulWidget {
  final Map<String, dynamic> placeData;
  final VoidCallback onClose;

  const PlaceDetailCard({
    super.key,
    required this.placeData,
    required this.onClose,
  });

  @override
  State<PlaceDetailCard> createState() => _PlaceDetailCardState();
}

class _PlaceDetailCardState extends State<PlaceDetailCard> {
  final ShareService _shareService = ShareService();
  final PlacesService _placesService = PlacesService();

  late bool _isLiked;
  late int _likeCount;
  late int _commentCount;

  @override
  void initState() {
    super.initState();
    _isLiked = widget.placeData['isLiked'] as bool? ?? false;
    _likeCount = (widget.placeData['likes'] as List?)?.length ?? 0;
    _commentCount = (widget.placeData['reviews'] as List?)?.length ?? 0;
  }

  void _onLike() {
    final placeId = widget.placeData['_id']['\$oid'];
    setState(() {
      _isLiked = !_isLiked;
      _likeCount += _isLiked ? 1 : -1;
    });
    _placesService.likePlace(placeId).catchError((e) {
      /* Handle error */
    });
  }

  void _onShare() {
    final placeName = widget.placeData['name'] ?? 'this cool place';
    _shareService.shareContent('Check out $placeName on Wicker!');
  }

  @override
  Widget build(BuildContext context) {
    bool hasImage =
        widget.placeData['photos'] != null &&
        (widget.placeData['photos'] as List).isNotEmpty;
    String imageUrl = '';

    if (hasImage) {
      final String baseUrl = defaultTargetPlatform == TargetPlatform.android
          ? "http://192.168.8.107:5000"
          : "http://127.0.0.1:5000";
      String imagePath = widget.placeData['photos'][0];
      imageUrl = '$baseUrl/${imagePath.replaceAll('\\', '/')}';
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                PlaceDetailScreen(placeId: widget.placeData['_id']['\$oid']),
          ),
        );
      },
      child: NeuCard(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 32),
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header Section
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NeuCard(
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.zero,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(13),
                    child: hasImage
                        ? Image.network(
                            imageUrl,
                            width: 60,
                            height: 60,
                            fit: BoxFit.cover,
                          )
                        : Container(
                            width: 60,
                            height: 60,
                            color: Colors.grey.shade200,
                            child: const Icon(
                              EvaIcons.imageOutline,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.placeData['name'] ?? 'No Name',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        widget.placeData['category'] ?? 'No Category',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: widget.onClose,
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.all(4),
                    shadowOffset: 2,
                    borderWidth: 2,
                    child: Icon(EvaIcons.close, size: 20),
                  ),
                ),
              ],
            ),
            const Divider(height: 24, thickness: 2, color: Colors.black),
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: _isLiked
                      ? EvaIcons.arrowUpward
                      : EvaIcons.arrowUpwardOutline,
                  label: _likeCount.toString(),
                  onTap: _onLike,
                  color: _isLiked ? Colors.white : Colors.black,
                  backgroundColor: _isLiked
                      ? const Color(0xFF4ECDC4)
                      : Colors.white,
                ),
                _buildActionButton(
                  icon: EvaIcons.messageSquareOutline,
                  label: _commentCount.toString(),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PlaceDetailScreen(
                          placeId: widget.placeData['_id']['\$oid'],
                        ),
                      ),
                    );
                  },
                ),
                _buildActionButton(
                  icon: EvaIcons.bookmarkOutline,
                  label: "Save",
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (context) => AddToPlaylistModal(
                        itemId: widget.placeData['_id']['\$oid'],
                        itemType: 'place',
                      ),
                    );
                  },
                ),
                _buildActionButton(
                  icon: EvaIcons.shareOutline,
                  label: "Share",
                  onTap: _onShare,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
    Color? backgroundColor,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: NeuCard(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 6),
          backgroundColor: backgroundColor ?? Colors.white,
          shadowOffset: 4,
          borderWidth: 2,
          borderRadius: 12,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 18, color: color ?? Colors.black),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: color ?? Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
