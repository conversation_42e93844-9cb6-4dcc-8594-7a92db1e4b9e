import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart'; // Import LatLng
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient
import 'package:wicker/services/analytics_service.dart';

class SearchService {
  final ConfigService _config = ConfigService.instance;
  final WickerHttpClient _client = WickerHttpClient();
  final AnalyticsService _analytics = AnalyticsService(); // Add this instance

  Future<Map<String, dynamic>> search(String query, {LatLng? location}) async {
    // --- NEW: Log the search event ---
    _analytics.logEvent('search', {'query': query});
    // --- End of NEW ---

    final baseUrl = await _config.getBaseUrl();
    try {
      final Map<String, dynamic> body = {'query': query};
      if (location != null) {
        body['location'] = {
          'lat': location.latitude,
          'lon': location.longitude,
        };
      }

      final response = await _client.post(
        Uri.parse('$baseUrl/api/search/'),
        headers: {'Content-Type': 'application/json; charset=UTF-8'},
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to perform search. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Search service error: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> instantSearch(String query) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/api/search/instant'),
        headers: {'Content-Type': 'application/json; charset=UTF-8'},
        body: jsonEncode({'query': query}),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to perform instant search. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Instant search service error: $e');
      rethrow;
    }
  }
}
