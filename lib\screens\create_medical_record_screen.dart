import 'package:flutter/material.dart';
import 'package:wicker/services/medical_service.dart';
import 'package:wicker/services/crypto_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreateMedicalRecordScreen extends StatefulWidget {
  const CreateMedicalRecordScreen({super.key});

  @override
  _CreateMedicalRecordScreenState createState() =>
      _CreateMedicalRecordScreenState();
}

class _CreateMedicalRecordScreenState extends State<CreateMedicalRecordScreen> {
  final MedicalService _medicalService = MedicalService();
  final CryptoService _cryptoService = CryptoService();
  final _patientIdController = TextEditingController();
  final _recordController = TextEditingController();
  bool _isLoading = false;

  void _createRecord() async {
    if (_patientIdController.text.isEmpty || _recordController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please fill all fields')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final encryptedRecord = await _cryptoService.encrypt(
        _recordController.text,
      );
      await _medicalService.createMedicalRecord(
        _patientIdController.text,
        encryptedRecord,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Medical record created successfully')),
      );
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(e.toString())));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Medical Record')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            NeuTextFormField(
              controller: _patientIdController,
              labelText: 'Patient ID',
            ),
            const SizedBox(height: 16),
            NeuTextFormField(
              controller: _recordController,
              labelText: 'Medical Record',
              maxLines: 5,
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _createRecord,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Create Record'),
            ),
          ],
        ),
      ),
    );
  }
}
