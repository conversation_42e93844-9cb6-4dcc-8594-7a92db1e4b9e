import 'package:flutter/material.dart';
import 'package:wicker/services/medical_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreateMedicalRecordScreen extends StatefulWidget {
  const CreateMedicalRecordScreen({super.key});

  @override
  _CreateMedicalRecordScreenState createState() =>
      _CreateMedicalRecordScreenState();
}

class _CreateMedicalRecordScreenState extends State<CreateMedicalRecordScreen> {
  final MedicalService _medicalService = MedicalService();
  final _patientIdController = TextEditingController();
  final _recordController = TextEditingController();
  bool _isLoading = false;

  void _createRecord() async {
    if (_patientIdController.text.isEmpty || _recordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all fields')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _medicalService.createMedicalRecord(
        _patientIdController.text,
        _recordController.text,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Medical record created successfully')),
      );
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Medical Record'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            NeuCard(
              child: TextField(
                controller: _patientIdController,
                decoration: const InputDecoration(
                  labelText: 'Patient ID',
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(height: 16),
            NeuCard(
              child: TextField(
                controller: _recordController,
                decoration: const InputDecoration(
                  labelText: 'Encrypted Medical Record',
                  border: InputBorder.none,
                ),
                maxLines: 5,
              ),
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _createRecord,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Create Record'),
            ),
          ],
        ),
      ),
    );
  }
}
