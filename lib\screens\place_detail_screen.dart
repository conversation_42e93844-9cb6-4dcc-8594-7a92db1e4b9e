// import 'package:eva_icons_flutter/eva_icons_flutter.dart';
// import 'package:flutter/material.dart';
// import 'package:jwt_decoder/jwt_decoder.dart';
// import 'package:wicker/services/auth_service.dart';
// import 'package:wicker/services/config_service.dart';
// import 'package:wicker/services/ecommerce_service.dart';
// import 'package:wicker/services/places_service.dart';
// import 'package:wicker/widgets/neubrutalist_widgets.dart';
// import 'package:wicker/widgets/post_card.dart';
// import 'package:wicker/widgets/product_card.dart';

// class PlaceDetailScreen extends StatefulWidget {
//   final String placeId;

//   const PlaceDetailScreen({super.key, required this.placeId});

//   @override
//   State<PlaceDetailScreen> createState() => _PlaceDetailScreenState();
// }

// class _PlaceDetailScreenState extends State<PlaceDetailScreen>
//     with SingleTickerProviderStateMixin {
//   final PlacesService _placesService = PlacesService();
//   final EcommerceService _ecommerceService = EcommerceService();
//   final ConfigService _configService = ConfigService.instance;
//   final AuthService _authService = AuthService();

//   late Future<Map<String, dynamic>> _detailsFuture;
//   late Future<String> _baseUrlFuture;
//   late TabController _tabController;
//   Future<List<Map<String, dynamic>>>? _productsFuture;
//   String? _currentUserId;

//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 3, vsync: this);
//     _initialize();
//   }

//   void _initialize() async {
//     // Get the current user's ID first
//     final token = await _authService.getAccessToken();
//     if (token != null) {
//       _currentUserId = _authService.getUserIdFromToken(token);
//     }
//     _fetchDetails();
//   }

//   void _fetchDetails() {
//     setState(() {
//       _detailsFuture = _placesService.getPlaceDetails(widget.placeId);
//       // Chain the product fetch to the details future
//       _detailsFuture.then((placeData) {
//         final businessId = placeData['business_id']?['\$oid'];
//         if (businessId != null) {
//           _fetchBusinessProducts(businessId);
//         }
//       });
//     });
//   }

//   void _fetchBusinessProducts(String businessId) {
//     setState(() {
//       _productsFuture = _ecommerceService.getBusinessProducts(businessId);
//     });
//   }

//   Future<void> _handleClaimPlace() async {
//     try {
//       final message = await _placesService.claimPlace(widget.placeId);
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(message), backgroundColor: Colors.green),
//         );
//         // Refresh the screen to hide the button and show updated state
//         _fetchDetails();
//       }
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text(
//               'Claim Failed: ${e.toString().replaceAll("Exception: ", "")}',
//             ),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     }
//   }

//   @override
//   void dispose() {
//     _tabController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       body: FutureBuilder<List<dynamic>>(
//         future: Future.wait([_detailsFuture, _baseUrlFuture]),
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           }
//           if (snapshot.hasError || !snapshot.hasData) {
//             return Center(
//               child: NeuCard(
//                 backgroundColor: const Color(0xFFFF6B6B),
//                 child: Text(
//                   'Error: ${snapshot.error ?? "Could not load details."}',
//                 ),
//               ),
//             );
//           }

//           final place = snapshot.data![0] as Map<String, dynamic>;
//           final baseUrl = snapshot.data![1] as String;
//           final posts = List<Map<String, dynamic>>.from(
//             place['tagged_posts'] ?? [],
//           );

//           bool hasImage =
//               place['photos'] != null && (place['photos'] as List).isNotEmpty;
//           String imageUrl = '';

//           if (hasImage) {
//             String imagePath = place['photos'][0];
//             imageUrl = '$baseUrl/${imagePath.replaceAll('\\', '/')}';
//           } else {
//             imageUrl =
//                 'https://picsum.photos/seed/${place['_id']['\$oid']}/800/400';
//           }

//           return NestedScrollView(
//             headerSliverBuilder: (context, innerBoxIsScrolled) {
//               return [
//                 SliverAppBar(
//                   expandedHeight: 250.0,
//                   floating: false,
//                   pinned: true,
//                   backgroundColor: const Color(0xFF6C5CE7),
//                   leading: Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: GestureDetector(
//                       onTap: () => Navigator.of(context).pop(),
//                       child: const NeuCard(
//                         margin: EdgeInsets.zero,
//                         padding: EdgeInsets.zero,
//                         child: Center(
//                           child: Icon(Icons.arrow_back, color: Colors.black),
//                         ),
//                       ),
//                     ),
//                   ),
//                   flexibleSpace: FlexibleSpaceBar(
//                     centerTitle: true,
//                     titlePadding: const EdgeInsets.symmetric(
//                       vertical: 12,
//                       horizontal: 48,
//                     ),
//                     title: NeuCard(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 12,
//                         vertical: 6,
//                       ),
//                       margin: EdgeInsets.zero,
//                       child: Text(
//                         place['name'],
//                         style: const TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.bold,
//                           fontSize: 16,
//                         ),
//                         overflow: TextOverflow.ellipsis,
//                       ),
//                     ),
//                     background: Stack(
//                       fit: StackFit.expand,
//                       children: [
//                         Image.network(imageUrl, fit: BoxFit.cover),
//                         Container(
//                           decoration: BoxDecoration(
//                             gradient: LinearGradient(
//                               begin: Alignment.topCenter,
//                               end: Alignment.bottomCenter,
//                               colors: [
//                                 Colors.transparent,
//                                 Colors.black.withOpacity(0.4),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ];
//             },
//             body: Column(
//               children: [
//                 NeuCard(
//                   margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
//                   padding: EdgeInsets.zero,
//                   child: TabBar(
//                     controller: _tabController,
//                     indicator: const BoxDecoration(
//                       color: Color(0xFFFFE66D),
//                       border: Border(
//                         bottom: BorderSide(color: Colors.black, width: 3.0),
//                       ),
//                     ),
//                     indicatorSize: TabBarIndicatorSize.tab,
//                     labelColor: Colors.black,
//                     unselectedLabelColor: Colors.grey.shade700,
//                     labelStyle: const TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 14,
//                     ),
//                     tabs: const [
//                       Tab(icon: Icon(Icons.info_outline), text: 'About'),
//                       Tab(icon: Icon(Icons.post_add), text: 'Posts'),
//                       Tab(
//                         icon: Icon(Icons.shopping_cart_outlined),
//                         text: 'Shop',
//                       ),
//                     ],
//                   ),
//                 ),
//                 Expanded(
//                   child: TabBarView(
//                     controller: _tabController,
//                     children: [
//                       _buildAboutTab(place),
//                       _buildPostsTab(posts),
//                       _buildShopTab(),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }

//   Future<String?> _getCurrentUserId() async {
//     try {
//       final token = await _authService.getAccessToken();
//       if (token == null) return null;

//       final decodedToken = JwtDecoder.decode(token);
//       return decodedToken['sub'];
//     } catch (e) {
//       print('Error getting current user ID: $e');
//       return null;
//     }
//   }

//   Widget _buildAboutTab(Map<String, dynamic> place) {
//     // final bool canClaim = (_currentUserId == creatorId && isUnclaimed);

//     return FutureBuilder<String?>(
//       future: _getCurrentUserId(),
//       builder: (context, snapshot) {
//         final bool isUnclaimed = place['business_id'] == null;

//         final currentUserId = snapshot.data;
//         final creatorId = place['created_by']['\$oid'];
//         final bool canClaim =
//             (currentUserId == creatorId && place['business_id'] == null);

//         return ListView(
//           padding: const EdgeInsets.all(16.0),
//           children: [
//             NeuCard(
//               backgroundColor: const Color(0xFF4ECDC4),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const Text(
//                     'PLACE INFO',
//                     style: TextStyle(
//                       fontSize: 20,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.white,
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   Wrap(
//                     children: [
//                       NeuChip(
//                         label: place['category'] ?? 'Unknown',
//                         icon: Icons.category,
//                         backgroundColor: const Color(0xFFFFE66D),
//                         textColor: Colors.black,
//                       ),
//                       if (place['business_id'] != null)
//                         const NeuChip(
//                           label: 'VERIFIED',
//                           icon: Icons.verified,
//                           backgroundColor: Color(0xFF00D2D3),
//                         ),
//                       //if ratio of likes to dislikes > 1
//                       if (place['likes'] != null &&
//                           place['dislikes'] != null &&
//                           place['dislikes'].length > 0) ...[
//                         () {
//                           double ratio =
//                               place['likes'].length / place['dislikes'].length;
//                           if (ratio > 1) {
//                             return const NeuChip(
//                               label: 'POPULAR',
//                               icon: Icons.trending_up,
//                               backgroundColor: Color(0xFFFF6B6B),
//                             );
//                           }
//                           return const SizedBox.shrink();
//                         }(),
//                       ],
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             if (canClaim)
//               Padding(
//                 padding: const EdgeInsets.only(top: 16.0),
//                 child: GestureDetector(
//                   onTap: _handleClaimPlace,
//                   child: NeuCard(
//                     backgroundColor: const Color(0xFF00D2D3),
//                     child: const Center(
//                       child: Text(
//                         'Claim this Place for Your Business',
//                         style: TextStyle(
//                           color: Colors.white,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         );
//       },
//     );
//   }

//   Widget _buildPostsTab(List<Map<String, dynamic>> posts) {
//     if (posts.isEmpty) {
//       return Center(
//         child: NeuCard(
//           backgroundColor: const Color(0xFFFFE66D),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Icon(
//                 Icons.photo_library_outlined,
//                 size: 48,
//                 color: Colors.grey[800],
//               ),
//               const SizedBox(height: 16),
//               const Text(
//                 'NO POSTS YET',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               Text(
//                 'Be the first to share a post here!',
//                 style: TextStyle(color: Colors.grey[800]),
//                 textAlign: TextAlign.center,
//               ),
//             ],
//           ),
//         ),
//       );
//     }
//     return ListView.builder(
//       padding: const EdgeInsets.symmetric(vertical: 8),
//       itemCount: posts.length,
//       itemBuilder: (context, index) {
//         return PostCard(postData: posts[index]);
//       },
//     );
//   }

//   Widget _buildShopTab() {
//     if (_productsFuture == null) {
//       return Center(
//         child: NeuCard(
//           backgroundColor: const Color(0xFFFFE66D),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Icon(Icons.storefront, size: 48, color: Colors.grey[800]),
//               const SizedBox(height: 16),
//               const Text(
//                 'NO SHOP AVAILABLE',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               Text(
//                 'This place has not been linked to a business.',
//                 style: TextStyle(color: Colors.grey[800]),
//                 textAlign: TextAlign.center,
//               ),
//             ],
//           ),
//         ),
//       );
//     }

//     return FutureBuilder<List<Map<String, dynamic>>>(
//       future: _productsFuture,
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return const Center(child: CircularProgressIndicator());
//         }
//         if (snapshot.hasError) {
//           return Center(
//             child: Text("Error loading products: ${snapshot.error}"),
//           );
//         }
//         if (!snapshot.hasData || snapshot.data!.isEmpty) {
//           return Center(
//             child: NeuCard(
//               backgroundColor: const Color(0xFFFFE66D),
//               child: const Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Icon(Icons.storefront, size: 48),
//                   SizedBox(height: 16),
//                   Text(
//                     'NO PRODUCTS YET',
//                     style: TextStyle(fontWeight: FontWeight.bold),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }

//         final products = snapshot.data!;
//         return ListView.builder(
//           padding: const EdgeInsets.symmetric(vertical: 8),
//           itemCount: products.length,
//           itemBuilder: (context, index) {
//             return ProductCard(productData: products[index]);
//           },
//         );
//       },
//     );
//   }
// }

import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/screens/business_detail_screen.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/widgets/product_card.dart';

class PlaceDetailScreen extends StatefulWidget {
  final String placeId;

  const PlaceDetailScreen({super.key, required this.placeId});

  @override
  State<PlaceDetailScreen> createState() => _PlaceDetailScreenState();
}

class _PlaceDetailScreenState extends State<PlaceDetailScreen>
    with SingleTickerProviderStateMixin {
  final PlacesService _placesService = PlacesService();
  final EcommerceService _ecommerceService = EcommerceService();
  final ConfigService _configService = ConfigService.instance;
  final AuthService _authService = AuthService();

  late Future<Map<String, dynamic>> _detailsFuture;
  late Future<String> _baseUrlFuture;
  late TabController _tabController;
  Future<List<Map<String, dynamic>>>? _productsFuture;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    // --- THE FIX: Initialize all futures immediately and synchronously ---
    _detailsFuture = _placesService.getPlaceDetails(widget.placeId);
    _baseUrlFuture = _configService.getBaseUrl();
    _tabController = TabController(length: 3, vsync: this);

    // Handle other async setup that depends on the futures
    _initializeDependentData();
  }

  void _initializeDependentData() async {
    // Get the current user's ID
    final token = await _authService.getAccessToken();
    if (token != null) {
      _currentUserId = _authService.getUserIdFromToken(token);
    }

    // After the place details are fetched, check if we need to fetch products
    _detailsFuture.then((placeData) {
      final businessId = placeData['business_id']?['\$oid'];
      if (businessId != null) {
        _fetchBusinessProducts(businessId);
      }
    });

    // Trigger a rebuild if the screen is still active, to show UI
    // elements that depend on _currentUserId (like the 'Claim' button).
    if (mounted) {
      setState(() {});
    }
  }

  void _refreshDetails() {
    // This function can be used to manually refresh the screen's data
    setState(() {
      _detailsFuture = _placesService.getPlaceDetails(widget.placeId);
      _initializeDependentData();
    });
  }

  void _fetchBusinessProducts(String businessId) {
    setState(() {
      _productsFuture = _ecommerceService.getBusinessProducts(businessId);
    });
  }

  Future<void> _handleClaimPlace() async {
    try {
      final message = await _placesService.claimPlace(widget.placeId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.green),
        );
        // Refresh the screen data to reflect the change
        _refreshDetails();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Claim Failed: ${e.toString().replaceAll("Exception: ", "")}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: FutureBuilder<List<dynamic>>(
        future: Future.wait([_detailsFuture, _baseUrlFuture]),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return Center(
              child: NeuCard(
                backgroundColor: const Color(0xFFFF6B6B),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Error: ${snapshot.error ?? "Could not load details."}',
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _refreshDetails,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            );
          }

          final place = snapshot.data![0] as Map<String, dynamic>;
          final baseUrl = snapshot.data![1] as String;
          final posts = List<Map<String, dynamic>>.from(
            place['tagged_posts'] ?? [],
          );

          bool hasImage =
              place['photos'] != null && (place['photos'] as List).isNotEmpty;
          String imageUrl = hasImage
              ? '$baseUrl/${(place['photos'][0] as String).replaceAll('\\', '/')}'
              : 'https://picsum.photos/seed/${place['_id']['\$oid']}/800/400';

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  expandedHeight: 250.0,
                  floating: false,
                  pinned: true,
                  backgroundColor: const Color(0xFF6C5CE7),
                  leading: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const NeuCard(
                        margin: EdgeInsets.zero,
                        padding: EdgeInsets.zero,
                        child: Center(
                          child: Icon(Icons.arrow_back, color: Colors.black),
                        ),
                      ),
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    centerTitle: true,
                    titlePadding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 48,
                    ),
                    title: NeuCard(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      margin: EdgeInsets.zero,
                      child: Text(
                        place['name'],
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.network(imageUrl, fit: BoxFit.cover),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.4),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ];
            },
            body: Column(
              children: [
                NeuCard(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  padding: EdgeInsets.zero,
                  child: TabBar(
                    controller: _tabController,
                    indicator: const BoxDecoration(
                      color: Color(0xFFFFE66D),
                      border: Border(
                        bottom: BorderSide(color: Colors.black, width: 3.0),
                      ),
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.grey.shade700,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    tabs: const [
                      Tab(icon: Icon(Icons.info_outline), text: 'About'),
                      Tab(icon: Icon(Icons.post_add), text: 'Posts'),
                      Tab(
                        icon: Icon(Icons.shopping_cart_outlined),
                        text: 'Shop',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAboutTab(place),
                      _buildPostsTab(posts),
                      _buildShopTab(),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Widget _buildAboutTab(Map<String, dynamic> place) {
  //   final creatorId = place['created_by']['\$oid'];
  //   final bool isUnclaimed = place['business_id'] == null;
  //   final bool canClaim = (_currentUserId == creatorId && isUnclaimed);

  //   return ListView(
  //     padding: const EdgeInsets.all(16.0),
  //     children: [
  //       NeuCard(
  //         backgroundColor: const Color(0xFF4ECDC4),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             const Text(
  //               'PLACE INFO',
  //               style: TextStyle(
  //                 fontSize: 20,
  //                 fontWeight: FontWeight.bold,
  //                 color: Colors.white,
  //               ),
  //             ),
  //             const SizedBox(height: 16),
  //             Wrap(
  //               spacing: 8,
  //               runSpacing: 8,
  //               children: [
  //                 NeuChip(
  //                   label: place['category'] ?? 'Unknown',
  //                   icon: Icons.category,
  //                   backgroundColor: const Color(0xFFFFE66D),
  //                   textColor: Colors.black,
  //                 ),
  //                 if (place['business_id'] != null)
  //                   const NeuChip(
  //                     label: 'VERIFIED',
  //                     icon: Icons.verified,
  //                     backgroundColor: Color(0xFF00D2D3),
  //                   ),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ),
  //       if (canClaim)
  //         Padding(
  //           padding: const EdgeInsets.only(top: 16.0),
  //           child: GestureDetector(
  //             onTap: _handleClaimPlace,
  //             child: NeuCard(
  //               backgroundColor: const Color(0xFF00D2D3),
  //               child: const Center(
  //                 child: Text(
  //                   'Claim this Place for Your Business',
  //                   style: TextStyle(
  //                     color: Colors.white,
  //                     fontWeight: FontWeight.bold,
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ),
  //     ],
  //   );
  // }

  Widget _buildAboutTab(Map<String, dynamic> place) {
    final creatorId = place['created_by']['\$oid'];
    final bool isUnclaimed = place['business_id'] == null;
    final bool canClaim = (_currentUserId == creatorId && isUnclaimed);

    // --- NEW: Get the list of other businesses from the data ---
    final otherBusinesses = List<Map<String, dynamic>>.from(
      place['other_businesses'] ?? [],
    );

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        NeuCard(
          backgroundColor: const Color(0xFF4ECDC4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'PLACE INFO',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  NeuChip(
                    label: place['category'] ?? 'Unknown',
                    icon: Icons.category,
                    backgroundColor: const Color(0xFFFFE66D),
                    textColor: Colors.black,
                  ),
                  if (place['business_id'] != null)
                    const NeuChip(
                      label: 'VERIFIED',
                      icon: Icons.verified,
                      backgroundColor: Color(0xFF00D2D3),
                    ),
                ],
              ),
            ],
          ),
        ),
        if (canClaim)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: GestureDetector(
              onTap: _handleClaimPlace,
              child: NeuCard(
                backgroundColor: const Color(0xFF00D2D3),
                child: const Center(
                  child: Text(
                    'Claim this Place for Your Business',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),

        // --- NEW: "More from this Owner" Section ---
        if (otherBusinesses.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "More from this Owner",
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 80,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: otherBusinesses.length,
                    itemBuilder: (context, index) {
                      return _buildOtherBusinessChip(otherBusinesses[index]);
                    },
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // --- NEW: Helper widget for the business chips ---
  Widget _buildOtherBusinessChip(Map<String, dynamic> businessData) {
    final List<dynamic> images = businessData['images'] as List<dynamic>? ?? [];
    final String? imagePath = images.isNotEmpty
        ? images.first.toString()
        : null;

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                BusinessDetailScreen(businessData: businessData),
          ),
        );
      },
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(right: 12),
        child: NeuCard(
          margin: EdgeInsets.zero,
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: SizedBox(
                    width: 50,
                    height: 50,
                    child: imagePath != null
                        ? FutureBuilder<String>(
                            future: ConfigService.instance.getBaseUrl(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                final fullUrl =
                                    '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                                return Image.network(
                                  fullUrl,
                                  fit: BoxFit.cover,
                                );
                              }
                              return const SizedBox();
                            },
                          )
                        : Container(
                            color: Colors.grey.shade200,
                            child: const Icon(
                              Icons.storefront,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  businessData['business_name'] ?? '',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostsTab(List<Map<String, dynamic>> posts) {
    if (posts.isEmpty) {
      return Center(
        child: NeuCard(
          backgroundColor: const Color(0xFFFFE66D),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 48,
                color: Colors.grey[800],
              ),
              const SizedBox(height: 16),
              const Text(
                'NO POSTS YET',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Be the first to share a post here!',
                style: TextStyle(color: Colors.grey[800]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        return PostCard(postData: posts[index]);
      },
    );
  }

  Widget _buildShopTab() {
    if (_productsFuture == null) {
      return Center(
        child: NeuCard(
          backgroundColor: const Color(0xFFFFE66D),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.storefront, size: 48, color: Colors.grey[800]),
              const SizedBox(height: 16),
              const Text(
                'NO SHOP AVAILABLE',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'This place has not been linked to a business.',
                style: TextStyle(color: Colors.grey[800]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _productsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(
            child: Text("Error loading products: ${snapshot.error}"),
          );
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: NeuCard(
              backgroundColor: const Color(0xFFFFE66D),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.storefront, size: 48),
                  SizedBox(height: 16),
                  Text(
                    'NO PRODUCTS YET',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          );
        }

        final products = snapshot.data!;
        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: products.length,
          itemBuilder: (context, index) {
            return ProductCard(productData: products[index]);
          },
        );
      },
    );
  }
}
