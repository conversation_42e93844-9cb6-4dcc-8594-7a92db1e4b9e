import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/chat_service.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/message_bubble.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class ChatScreen extends StatefulWidget {
  final String roomId;
  final String recipientName;

  const ChatScreen({
    super.key,
    required this.roomId,
    required this.recipientName,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ChatService _chatService = ChatService();
  final AuthService _authService = AuthService();
  final List<Map<String, dynamic>> _messages = [];
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  StreamSubscription? _messageSubscription;
  String? _currentUserId;
  String? _currentUsername;
  bool _isLoadingHistory = true;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    // Get current user details for sending messages
    final token = await _authService.getAccessToken();
    if (token != null) {
      _currentUserId = _authService.getUserIdFromToken(token);
      // In a real app, you'd get the username from a profile service,
      // but we can decode it from the token if you have it there, or fetch it.
      // For now, let's assume we can fetch it. We will use a placeholder.
    }

    final myProfile = await UserService().getProfileHeader();
    _currentUsername = myProfile['username'];

    await _chatService.markMessagesAsRead(widget.roomId);

    // Connect to the socket server
    await _chatService.connect();
    _chatService.joinRoom(widget.roomId);

    // Listen for incoming messages
    _messageSubscription = _chatService.messages.listen((message) {
      if (mounted) {
        setState(() {
          _messages.add(message);
        });
        _scrollToBottom();
      }
    });

    // Fetch initial message history
    final history = await _chatService.fetchHistory(widget.roomId);
    if (mounted) {
      setState(() {
        _messages.addAll(history);
        _isLoadingHistory = false;
      });
      _scrollToBottom();
    }
  }

  void _sendMessage() {
    if (_textController.text.trim().isEmpty) return;
    if (_currentUserId == null || _currentUsername == null) return;

    _chatService.sendMessage(
      text: _textController.text.trim(),
      roomId: widget.roomId,
      authorId: _currentUserId!,
      authorUsername: _currentUsername!,
    );

    _textController.clear();
    _scrollToBottom();
  }

  void _scrollToBottom() {
    // A short delay ensures the list has built before we try to scroll.
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _chatService.leaveRoom(widget.roomId);
    _chatService.disconnect();
    _messageSubscription?.cancel();
    _textController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: AppBar(title: Text('Chat with ${widget.recipientName}')),
      body: Column(
        children: [
          Expanded(
            child: _isLoadingHistory
                ? const Center(child: CircularProgressIndicator())
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16.0),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final msg = _messages[index];
                      return MessageBubble(
                        text: msg['text'] ?? '',
                        sender: msg['author_username'] ?? 'User',
                        isMe: msg['author_id']['\$oid'] == _currentUserId,
                      );
                    },
                  ),
          ),
          _buildMessageInputField(),
        ],
      ),
    );
  }

  Widget _buildMessageInputField() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: Colors.white,
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  controller: _textController,
                  decoration: const InputDecoration(
                    hintText: 'Type a message...',
                  ),
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: _sendMessage,
              child: const NeuCard(
                margin: EdgeInsets.zero,
                backgroundColor: Color(0xFF00D2D3),
                child: Icon(Icons.send, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
