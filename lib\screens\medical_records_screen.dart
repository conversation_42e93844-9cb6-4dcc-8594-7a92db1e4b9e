import 'package:flutter/material.dart';
import 'package:wicker/models/medical_record.dart';
import 'package:wicker/services/medical_service.dart';
import 'package:wicker/widgets/medical_record_card.dart';
import 'package:wicker/services/auth_service.dart';

class MedicalRecordsScreen extends StatefulWidget {
  final String? patientId;
  const MedicalRecordsScreen({super.key, this.patientId});

  @override
  _MedicalRecordsScreenState createState() => _MedicalRecordsScreenState();
}

class _MedicalRecordsScreenState extends State<MedicalRecordsScreen> {
  final MedicalService _medicalService = MedicalService();
  final AuthService _authService = AuthService();
  late Future<List<MedicalRecord>> _medicalRecordsFuture;

  @override
  void initState() {
    super.initState();
    _medicalRecordsFuture = _fetchMedicalRecords();
  }

  Future<List<MedicalRecord>> _fetchMedicalRecords() async {
    final roles = await _authService.getRoles();
    if (roles.contains('doctor')) {
      if (widget.patientId != null) {
        return _medicalService.getMedicalRecordsForPatient(widget.patientId!);
      } else {
        return _medicalService.getMedicalRecordsForDoctor();
      }
    } else {
      final userId = await _authService.getUserId();
      return _medicalService.getMedicalRecordsForPatient(userId!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Medical Records'),
      ),
      body: FutureBuilder<List<MedicalRecord>>(
        future: _medicalRecordsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No medical records found.'));
          }

          final medicalRecords = snapshot.data!;

          return ListView.builder(
            itemCount: medicalRecords.length,
            itemBuilder: (context, index) {
              final record = medicalRecords[index];
              return MedicalRecordCard(record: record);
            },
          );
        },
      ),
    );
  }
}
