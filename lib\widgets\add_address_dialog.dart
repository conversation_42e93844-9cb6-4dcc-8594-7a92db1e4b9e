import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class AddAddressDialog extends StatelessWidget {
  final TextEditingController labelController;
  final TextEditingController addressController;
  final VoidCallback onAdd;

  const AddAddressDialog({
    super.key,
    required this.labelController,
    required this.addressController,
    required this.onAdd,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add New Address'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          NeuTextForm<PERSON>ield(
            controller: labelController,
            labelText: 'Label',
          ),
          const Sized<PERSON><PERSON>(height: 16),
          NeuText<PERSON><PERSON><PERSON><PERSON>(
            controller: addressController,
            labelText: 'Address',
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        NeuButton(
          onPressed: onAdd,
          child: const Text('Add'),
        ),
      ],
    );
  }
}
