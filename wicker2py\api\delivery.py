import os
from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required
from bson import ObjectId
from dotenv import load_dotenv
from math import radians, sin, cos, sqrt, atan2




delivery_bp = Blueprint('delivery_bp', __name__)

# --- CONFIGURATION ---
# These values can be moved to environment variables in a production app
BASE_DELIVERY_FEE = float(os.getenv('BASE_DELIVERY_FEE'))
FEE_PER_KM = float(os.getenv('FEE_PER_KM'))

def haversine_distance(lat1, lon1, lat2, lon2):
    """Calculates the distance between two points in kilometers."""
    R = 6371.0  # Earth radius in kilometers

    lat1_rad, lon1_rad = radians(lat1), radians(lon1)
    lat2_rad, lon2_rad = radians(lat2), radians(lon2)

    dlon = lon2_rad - lon1_rad
    dlat = lat2_rad - lat1_rad

    a = sin(dlat / 2)**2 + cos(lat1_rad) * cos(lat2_rad) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    distance = R * c
    return distance



@delivery_bp.route('/calculate-fee', methods=['POST'])
@jwt_required()
def calculate_delivery_fee():
    """
    Calculates the delivery fee based on the distance between a business
    and a buyer's specified location, using unified location logic.
    """
    db = delivery_bp.db
    data = request.get_json()
    
    business_id = data.get('business_id')
    buyer_location = data.get('buyer_location')

    if not business_id or not buyer_location:
        return jsonify({"msg": "business_id and buyer_location are required"}), 400

    business = db.businesses.find_one({"_id": ObjectId(business_id)})
    if not business:
        return jsonify({"msg": "Business not found"}), 404
    
    seller_coords = None
    
    # --- REFACTORED: Unified Location Logic ---
    # 1. Prioritize the private pickup_location field if it exists
    if 'pickup_location' in business:
        seller_coords = business['pickup_location']['coordinates']
    # 2. If not, check if the business is linked to a place
    elif 'linked_place_id' in business:
        linked_place = db.places.find_one({"_id": business['linked_place_id']})
        if linked_place and 'location' in linked_place:
            seller_coords = linked_place['location']['coordinates']
            
    if seller_coords is None:
        return jsonify({"msg": "This vendor has not set a pickup location for deliveries."}), 400
    # --- End of REFACTOR ---
        
    seller_lon, seller_lat = seller_coords[0], seller_coords[1]
    
    buyer_lat = buyer_location.get('lat')
    buyer_lon = buyer_location.get('lon')

    if buyer_lat is None or buyer_lon is None:
        return jsonify({"msg": "Invalid buyer location format"}), 400

    distance_km = haversine_distance(seller_lat, seller_lon, buyer_lat, buyer_lon)
    delivery_fee = BASE_DELIVERY_FEE + (distance_km * FEE_PER_KM)

    return jsonify({
        "distance_km": round(distance_km, 2),
        "delivery_fee": round(delivery_fee, 2)
    }), 200