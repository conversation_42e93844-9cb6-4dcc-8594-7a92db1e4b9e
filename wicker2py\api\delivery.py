import os
from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
from dotenv import load_dotenv
from math import radians, sin, cos, sqrt, atan2
import datetime


delivery_bp = Blueprint('delivery_bp', __name__)

# --- CONFIGURATION ---
# These values can be moved to environment variables in a production app
BASE_DELIVERY_FEE = float(os.getenv('BASE_DELIVERY_FEE'))
FEE_PER_KM = float(os.getenv('FEE_PER_KM'))

def haversine_distance(lat1, lon1, lat2, lon2):
    """Calculates the distance between two points in kilometers."""
    R = 6371.0  # Earth radius in kilometers

    lat1_rad, lon1_rad = radians(lat1), radians(lon1)
    lat2_rad, lon2_rad = radians(lat2), radians(lon2)

    dlon = lon2_rad - lon1_rad
    dlat = lat2_rad - lat1_rad

    a = sin(dlat / 2)**2 + cos(lat1_rad) * cos(lat2_rad) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    distance = R * c
    return distance



@delivery_bp.route('/calculate-fee', methods=['POST'])
@jwt_required()
def calculate_delivery_fee():
    """
    Calculates the delivery fee based on the distance between a business
    and a buyer's specified location, using unified location logic.
    """
    db = delivery_bp.db
    data = request.get_json()
    
    business_id = data.get('business_id')
    buyer_location = data.get('buyer_location')

    if not business_id or not buyer_location:
        return jsonify({"msg": "business_id and buyer_location are required"}), 400

    business = db.businesses.find_one({"_id": ObjectId(business_id)})
    if not business:
        return jsonify({"msg": "Business not found"}), 404
    
    seller_coords = None
    
    # --- REFACTORED: Unified Location Logic ---
    # 1. Prioritize the private pickup_location field if it exists
    if 'pickup_location' in business:
        seller_coords = business['pickup_location']['coordinates']
    # 2. If not, check if the business is linked to a place
    elif 'linked_place_id' in business:
        linked_place = db.places.find_one({"_id": business['linked_place_id']})
        if linked_place and 'location' in linked_place:
            seller_coords = linked_place['location']['coordinates']
            
    if seller_coords is None:
        return jsonify({"msg": "This vendor has not set a pickup location for deliveries."}), 400
    # --- End of REFACTOR ---
        
    seller_lon, seller_lat = seller_coords[0], seller_coords[1]
    
    buyer_lat = buyer_location.get('lat')
    buyer_lon = buyer_location.get('lon')

    if buyer_lat is None or buyer_lon is None:
        return jsonify({"msg": "Invalid buyer location format"}), 400

    distance_km = haversine_distance(seller_lat, seller_lon, buyer_lat, buyer_lon)
    delivery_fee = BASE_DELIVERY_FEE + (distance_km * FEE_PER_KM)

    return jsonify({
        "distance_km": round(distance_km, 2),
        "delivery_fee": round(delivery_fee, 2)
    }), 200

@delivery_bp.route('/orders', methods=['POST'])
@jwt_required()
def create_delivery_order():
    db = delivery_bp.db
    sender_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    recipient_address_id = data.get('recipient_address_id')
    pickup_location = data.get('pickup_location')
    item_details = data.get('item_details')

    if not all([recipient_address_id, pickup_location, item_details]):
        return jsonify({"msg": "recipient_address_id, pickup_location, and item_details are required"}), 400

    # Verify the recipient address belongs to a user
    address = db.addresses.find_one({"_id": ObjectId(recipient_address_id)})
    if not address:
        return jsonify({"msg": "Recipient address not found"}), 404

    new_order = {
        "sender_id": sender_id,
        "recipient_address_id": ObjectId(recipient_address_id),
        "recipient_id": address['user_id'], # Store recipient_id for easier lookup
        "pickup_location": pickup_location, # In a real app, this would be more structured
        "item_details": item_details,
        "status": "pending", # Initial status
        "delivery_provider_id": None,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "updated_at": datetime.datetime.now(datetime.timezone.utc)
    }

    try:
        db.delivery_orders.insert_one(new_order)
        return jsonify({"msg": "Delivery order created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_bp.route('/orders', methods=['GET'])
@jwt_required()
def get_delivery_orders():
    db = delivery_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        orders = list(db.delivery_orders.find({
            "$or": [
                {"sender_id": current_user_id},
                {"recipient_id": current_user_id}
            ]
        }))
        from bson import json_util
        import json
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_bp.route('/orders/<order_id>', methods=['GET'])
@jwt_required()
def get_delivery_order(order_id):
    db = delivery_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        order = db.delivery_orders.find_one({"_id": ObjectId(order_id)})
        if not order:
            return jsonify({"msg": "Order not found"}), 404

        if order['sender_id'] != current_user_id and order['recipient_id'] != current_user_id:
            return jsonify({"msg": "You don't have permission to view this order"}), 403
        
        from bson import json_util
        import json
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@delivery_bp.route('/orders/<order_id>', methods=['PUT'])
@jwt_required()
def update_delivery_order_status(order_id):
    db = delivery_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    new_status = data.get('status')

    if not new_status:
        return jsonify({"msg": "Missing status"}), 400

    try:
        order = db.delivery_orders.find_one({"_id": ObjectId(order_id)})
        if not order:
            return jsonify({"msg": "Order not found"}), 404

        user = db.users.find_one({"_id": current_user_id})
        user_roles = user.get('roles', [])

        # Allow sender to cancel a pending order
        if order['sender_id'] == current_user_id and new_status == 'cancelled':
            if order['status'] == 'pending':
                db.delivery_orders.update_one(
                    {"_id": ObjectId(order_id)},
                    {"$set": {"status": new_status, "updated_at": datetime.datetime.now(datetime.timezone.utc)}}
                )
                return jsonify({"msg": "Order cancelled"}), 200
            else:
                return jsonify({"msg": "Only pending orders can be cancelled"}), 400

        # Allow delivery provider to update status
        if 'delivery_provider' in user_roles:
            allowed_statuses = ['accepted', 'in_progress', 'picked_up', 'delivered']
            if new_status not in allowed_statuses:
                return jsonify({"msg": f"Invalid status for delivery provider. Allowed statuses are: {allowed_statuses}"}), 400

            update_query = {"$set": {"status": new_status, "updated_at": datetime.datetime.now(datetime.timezone.utc)}}
            if new_status == 'accepted':
                update_query["$set"]["delivery_provider_id"] = current_user_id

            db.delivery_orders.update_one({"_id": ObjectId(order_id)}, update_query)
            return jsonify({"msg": f"Order status updated to {new_status}"}), 200

        return jsonify({"msg": "You don't have permission to update this order's status"}), 403

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
