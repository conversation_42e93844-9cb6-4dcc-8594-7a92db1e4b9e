import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class NotificationService extends ChangeNotifier {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  bool _hasUnreadAppointments = false;
  bool _hasUnreadChats = false;
  bool _hasPendingBookings = false;

  bool get hasUnreadAppointments => _hasUnreadAppointments;
  bool get hasUnreadChats => _hasUnreadChats;
  bool get hasPendingBookings => _hasPendingBookings;

  // A general getter to know if the main Hub button should glow
  bool get shouldHubGlow =>
      _hasUnreadAppointments || _hasUnreadChats || _hasPendingBookings;

  Future<void> fetchNotifications() async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/notifications/summary'),
      );

      if (response.statusCode == 200) {
        final summary = jsonDecode(response.body);
        _hasUnreadAppointments = summary['has_unread_appointments'] ?? false;
        _hasUnreadChats = summary['has_unread_chats'] ?? false;
        _hasPendingBookings = summary['has_pending_bookings'] ?? false;

        // This is the crucial step that tells all listening widgets to rebuild.
        notifyListeners();
      }
    } catch (e) {
      print('Failed to fetch notification summary: $e');
    }
  }
}
