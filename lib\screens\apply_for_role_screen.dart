import 'package:flutter/material.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class ApplyForRoleScreen extends StatefulWidget {
  const ApplyForRoleScreen({super.key});

  @override
  _ApplyForRoleScreenState createState() => _ApplyForRoleScreenState();
}

class _ApplyForRoleScreenState extends State<ApplyForRoleScreen> {
  final UserService _userService = UserService();
  String? _selectedRole;
  bool _isLoading = false;

  final List<String> _roles = ['delivery_provider', 'doctor'];

  void _submitApplication() async {
    if (_selectedRole == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a role')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _userService.applyForRole(_selectedRole!);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Application submitted successfully')),
      );
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Apply for a Role'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            NeuCard(
              child: DropdownButtonFormField<String>(
                value: _selectedRole,
                items: _roles.map((String role) {
                  return DropdownMenuItem<String>(
                    value: role,
                    child: Text(role.replaceAll('_', ' ').toUpperCase()),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedRole = newValue;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'Select a role',
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(height: 32),
            NeuButton(
              onPressed: _isLoading ? null : _submitApplication,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Submit Application'),
            ),
          ],
        ),
      ),
    );
  }
}
