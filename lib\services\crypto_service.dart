import 'package:bip39/bip39.dart' as bip39;
import 'package:encrypt/encrypt.dart' as enc;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class CryptoService {
  final _storage = const FlutterSecureStorage();
  static const _mnemonicStorageKey = 'mnemonic_phrase';

  Future<enc.Key> _deriveKeyFromMnemonic(String mnemonic) async {
    final seed = bip39.mnemonicToSeed(mnemonic);
    final key = enc.Key(seed.sublist(0, 32));
    return key;
  }

  Future<String> generateMnemonicAndKey() async {
    final mnemonic = bip39.generateMnemonic();
    await _storage.write(key: _mnemonicStorageKey, value: mnemonic);
    return mnemonic;
  }

  Future<enc.Key?> getKey() async {
    final mnemonic = await _storage.read(key: _mnemonicStorageKey);
    if (mnemonic == null) {
      return null;
    }
    return await _deriveKeyFromMnemonic(mnemonic);
  }

  Future<String?> getMnemonic() async {
    return await _storage.read(key: _mnemonicStorageKey);
  }

  Future<void> recoverKeyFromMnemonic(String mnemonic) async {
    // Validate the mnemonic before storing it
    if (!bip39.validateMnemonic(mnemonic)) {
      throw Exception('Invalid mnemonic phrase');
    }
    await _storage.write(key: _mnemonicStorageKey, value: mnemonic);
  }

  Future<String> encrypt(String plainText) async {
    final key = await getKey();
    if (key == null) {
      throw Exception('Encryption key not found. Please set up your account recovery.');
    }
    final iv = enc.IV.fromLength(16);
    final encrypter = enc.Encrypter(enc.AES(key));
    final encrypted = encrypter.encrypt(plainText, iv: iv);
    return '${iv.base64}:${encrypted.base64}';
  }

  Future<String> decrypt(String encryptedText) async {
    final key = await getKey();
    if (key == null) {
      throw Exception('Encryption key not found. Please set up your account recovery.');
    }
    final parts = encryptedText.split(':');
    final iv = enc.IV.fromBase64(parts[0]);
    final encrypted = enc.Encrypted.fromBase64(parts[1]);
    final encrypter = enc.Encrypter(enc.AES(key));
    return encrypter.decrypt(encrypted, iv: iv);
  }
}
