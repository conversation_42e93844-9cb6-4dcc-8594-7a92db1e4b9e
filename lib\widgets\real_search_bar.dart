import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class RealSearchBar extends StatefulWidget {
  final Function(String) onSearch;

  const RealSearchBar({super.key, required this.onSearch});

  @override
  _RealSearchBarState createState() => _RealSearchBarState();
}

class _RealSearchBarState extends State<RealSearchBar> {
  final _controller = TextEditingController();
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      widget.onSearch(_controller.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      borderRadius: 30.0,
      shadowOffset: 4.0,
      borderWidth: 2.0,
      child: TextField(
        controller: _controller,
        decoration: const InputDecoration(
          hintText: 'Search posts...',
          border: InputBorder.none,
          icon: Icon(Icons.search, color: Colors.grey),
        ),
      ),
    );
  }
}
