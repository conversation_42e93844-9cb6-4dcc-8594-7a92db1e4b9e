import 'dart:convert';
import 'package:wicker/models/user.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

import 'package:wicker/models/business_claim.dart';

class AdminService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<List<User>> getPendingDoctors() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/admin/pending-doctors'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => User.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get pending doctors');
    }
  }

  Future<List<User>> getPendingDeliveryProviders() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/admin/pending-delivery-providers'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => User.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get pending delivery providers');
    }
  }

  Future<void> verifyDoctor(String doctorId, bool approve) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/admin/verify-doctor/$doctorId'),
      body: jsonEncode({'approve': approve}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to verify doctor');
    }
  }

  Future<void> verifyDeliveryProvider(String providerId, bool approve) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/admin/verify-delivery-provider/$providerId'),
      body: jsonEncode({'approve': approve}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to verify delivery provider');
    }
  }

  Future<List<BusinessClaim>> getPendingBusinessClaims() async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/admin/pending-business-claims'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => BusinessClaim.fromJson(json)).toList();
    } else {
      throw Exception('Failed to get pending business claims');
    }
  }

  Future<void> verifyBusinessClaim(String claimId, bool approve) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/admin/verify-business-claim/$claimId'),
      body: jsonEncode({'approve': approve}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to verify business claim');
    }
  }
}
