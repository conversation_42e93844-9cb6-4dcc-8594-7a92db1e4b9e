from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
import json

bookings_bp = Blueprint('bookings_bp', __name__)

# --- VENDOR-FACING ENDPOINTS ---

@bookings_bp.route('/availability/<business_id>', methods=['POST'])
@jwt_required()
def set_availability(business_id):
    """ VENDOR-ONLY. Sets the weekly work schedule for a specific business. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    business = db.businesses.find_one({
        "_id": ObjectId(business_id),
        "owner_id": current_user_id
    })
    if not business:
        return jsonify({"msg": "Business not found or you do not have permission."}), 404

    availability_schedule = data.get('availability')
    if not isinstance(availability_schedule, dict):
        return jsonify({"msg": "Invalid availability format."}), 400

    try:
        db.businesses.update_one(
            {"_id": ObjectId(business_id)},
            {"$set": {"availability": availability_schedule}}
        )
        return jsonify({"msg": "Availability updated successfully."}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

# In flask_api/api/bookings.py

@bookings_bp.route('/my-schedule/<business_id>', methods=['GET'])
@jwt_required()
def get_my_schedule(business_id):
    """ VENDOR-ONLY. Fetches all bookings for a specific business owned by the user. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    # Verify ownership of the business
    business = db.businesses.find_one({"_id": ObjectId(business_id), "owner_id": current_user_id})
    if not business:
        return jsonify({"msg": "Business not found or you do not have permission."}), 404

    pipeline = [
        {'$match': {'business_id': ObjectId(business_id)}},
        {'$sort': {'start_time': 1}},
        {'$lookup': {
            'from': 'users', 'localField': 'client_id', 'foreignField': '_id', 'as': 'client_details'
        }},
        {'$lookup': {
            'from': 'products', 'localField': 'service_id', 'foreignField': '_id', 'as': 'service_details'
        }},
        {'$unwind': '$client_details'},
        {'$unwind': '$service_details'},
        {'$project': {
            'client_details.password_hash': 0, 'client_details.email': 0
        }}
    ]
    
    schedule = list(db.bookings.aggregate(pipeline))
    
    # --- THE FIX: Use json_util.dumps to handle MongoDB data types ---
    return Response(json_util.dumps(schedule), mimetype='application/json'), 200
    # --- End of FIX ---
@bookings_bp.route('/<booking_id>/confirm', methods=['POST'])
@jwt_required()
def confirm_booking(booking_id):
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    booking = db.bookings.find_one({"_id": ObjectId(booking_id)})
    if not booking: return jsonify({"msg": "Booking not found."}), 404
    if booking.get('seller_id') != current_user_id: return jsonify({"msg": "Not authorized."}), 403
    if booking.get('status') != 'pending_confirmation': return jsonify({"msg": "Booking not pending."}), 400

    # --- UPDATE: Set client_viewed to false to trigger a notification ---
    db.bookings.update_one(
        {"_id": ObjectId(booking_id)},
        {"$set": {"status": "confirmed", "client_viewed": False}}
    )
    return jsonify({"msg": "Booking confirmed."}), 200

@bookings_bp.route('/<booking_id>/decline', methods=['POST'])
@jwt_required()
def decline_booking(booking_id):
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    booking = db.bookings.find_one({"_id": ObjectId(booking_id)})
    if not booking: return jsonify({"msg": "Booking not found."}), 404
    if booking.get('seller_id') != current_user_id: return jsonify({"msg": "Not authorized."}), 403
    if booking.get('status') != 'pending_confirmation': return jsonify({"msg": "Booking not pending."}), 400

    # --- UPDATE: Set client_viewed to false to trigger a notification ---
    db.bookings.update_one(
        {"_id": ObjectId(booking_id)},
        {"$set": {"status": "declined", "client_viewed": False}}
    )
    return jsonify({"msg": "Booking declined."}), 200


# --- CLIENT-FACING ENDPOINTS ---

# --- NEW: Endpoint for clients to see their appointments ---
@bookings_bp.route('/my-appointments', methods=['GET'])
@jwt_required()
def get_my_appointments():
    """ CLIENT-ONLY. Fetches all bookings made by the current user. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    pipeline = [
        {'$match': {'client_id': current_user_id}},
        {'$sort': {'start_time': -1}}, # Show most recent first
        {'$lookup': {
            'from': 'businesses', 'localField': 'business_id', 'foreignField': '_id', 'as': 'business_details'
        }},
        {'$lookup': {
            'from': 'products', 'localField': 'service_id', 'foreignField': '_id', 'as': 'service_details'
        }},
        {'$unwind': '$business_details'},
        {'$unwind': '$service_details'},
    ]
    
    appointments = list(db.bookings.aggregate(pipeline))
    return jsonify(json.loads(json_util.dumps(appointments))), 200

# --- NEW: Lightweight endpoint to check for notifications ---
@bookings_bp.route('/notifications', methods=['GET'])
@jwt_required()
def check_notifications():
    """ CLIENT-ONLY. Checks if there are any unread booking status updates. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    unread_booking = db.bookings.find_one({
        "client_id": current_user_id,
        "client_viewed": False
    })
    
    return jsonify({"has_notifications": unread_booking is not None}), 200

# --- NEW: Endpoint to mark notifications as read ---
@bookings_bp.route('/mark-viewed', methods=['POST'])
@jwt_required()
def mark_bookings_viewed():
    """ CLIENT-ONLY. Marks all of a user's booking notifications as viewed. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    db.bookings.update_many(
        {"client_id": current_user_id, "client_viewed": False},
        {"$set": {"client_viewed": True}}
    )
    return jsonify({"msg": "Notifications marked as read."}), 200

@bookings_bp.route('/availability/<business_id>', methods=['GET'])
@jwt_required()
def get_availability(business_id):
    """
    CLIENT-FACING endpoint. Gets available time slots for a service, considering
    the vendor's schedule, existing bookings, and blocked-off break times.
    """
    db = bookings_bp.db
    date_str = request.args.get('date')
    service_id = request.args.get('service_id')

    if not date_str or not service_id:
        return jsonify({"msg": "A 'date' and 'service_id' query parameter are required."}), 400

    try:
        # 1. Get Service Duration
        service = db.products.find_one({"_id": ObjectId(service_id)})
        if not service: return jsonify({"msg": "Service not found"}), 404
        slot_duration_minutes = service.get('duration_minutes', 60)

        # 2. Get Vendor's General Schedule for the Day
        requested_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
        day_of_week = requested_date.strftime('%A').lower()
        business = db.businesses.find_one({"_id": ObjectId(business_id)})
        if not business or 'availability' not in business: return jsonify([]), 200
        day_schedule = business['availability'].get(day_of_week)
        if not day_schedule: return jsonify([]), 200

        # 3. Create a list of all unavailable periods (Bookings + Breaks)
        unavailable_periods = []
        
        # Add breaks from the vendor's schedule
        for break_period in day_schedule.get('breaks', []):
            unavailable_periods.append({
                "start": datetime.datetime.strptime(break_period['start'], '%H:%M').time(),
                "end": datetime.datetime.strptime(break_period['end'], '%H:%M').time()
            })
            
        # Add confirmed bookings
        start_of_day = datetime.datetime.combine(requested_date, datetime.time.min)
        end_of_day = datetime.datetime.combine(requested_date, datetime.time.max)
        booked_slots = list(db.bookings.find({
            "business_id": ObjectId(business_id),
            "status": "confirmed",
            "start_time": {"$gte": start_of_day, "$lt": end_of_day}
        }))
        for slot in booked_slots:
            unavailable_periods.append({
                "start": slot['start_time'].time(),
                "end": slot['end_time'].time()
            })

        # 4. Generate all potential slots and check for overlaps
        available_slots = []
        slot_delta = datetime.timedelta(minutes=slot_duration_minutes)
        
        start_work_time = datetime.datetime.strptime(day_schedule['start'], '%H:%M').time()
        end_work_time = datetime.datetime.strptime(day_schedule['end'], '%H:%M').time()
        
        # Convert to datetime objects for iteration
        current_slot_start = datetime.datetime.combine(requested_date, start_work_time)
        work_end_dt = datetime.datetime.combine(requested_date, end_work_time)

        while current_slot_start < work_end_dt:
            current_slot_end = current_slot_start + slot_delta
            if current_slot_end > work_end_dt:
                break

            is_available = True
            # Check if this potential slot overlaps with any unavailable period
            for period in unavailable_periods:
                period_start = datetime.datetime.combine(requested_date, period['start'])
                period_end = datetime.datetime.combine(requested_date, period['end'])
                # Overlap condition: (StartA < EndB) and (EndA > StartB)
                if current_slot_start < period_end and current_slot_end > period_start:
                    is_available = False
                    break
            
            if is_available:
                available_slots.append(current_slot_start.strftime('%H:%M'))
            
            current_slot_start += slot_delta

        return jsonify(available_slots), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500




@bookings_bp.route('/request', methods=['POST'])
@jwt_required()
def request_booking():
    """
    CLIENT-ONLY endpoint. Creates a new booking request for a service.
    """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    business_id = data.get('business_id')
    service_id = data.get('service_id')
    start_time_str = data.get('start_time')

    if not all([business_id, service_id, start_time_str]):
        return jsonify({"msg": "business_id, service_id, and start_time are required."}), 400

    try:
        start_time = datetime.datetime.fromisoformat(start_time_str)
        
        # --- REFACTORED: Fetch the service to get its actual duration ---
        service = db.products.find_one({"_id": ObjectId(service_id)})
        if not service:
            return jsonify({"msg": "Service (product) not found"}), 404
        
        duration = service.get('duration_minutes', 60) # Default to 60 mins if not set
        end_time = start_time + datetime.timedelta(minutes=duration)
        # --- End of REFACTOR ---

        business = db.businesses.find_one({"_id": ObjectId(business_id)})
        if not business:
            return jsonify({"msg": "Business not found"}), 404

        new_booking = {
            "client_id": current_user_id,
            "seller_id": business['owner_id'],
            "business_id": ObjectId(business_id),
            "service_id": ObjectId(service_id),
            "start_time": start_time,
            "end_time": end_time, # Use the accurately calculated end_time
            "status": "pending_confirmation",
            "created_at": datetime.datetime.now(datetime.timezone.utc)
        }
        
        db.bookings.insert_one(new_booking)

        return jsonify({"msg": "Booking request sent successfully. Awaiting vendor confirmation."}), 201

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    

# two new routes

@bookings_bp.route('/<booking_id>/complete', methods=['POST'])
@jwt_required()
def complete_booking(booking_id):
    """ VENDOR-ONLY. Marks a confirmed booking as completed. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    booking = db.bookings.find_one({"_id": ObjectId(booking_id), "seller_id": current_user_id})
    if not booking:
        return jsonify({"msg": "Booking not found or you are not the seller."}), 404

    db.bookings.update_one(
        {"_id": ObjectId(booking_id)},
        {"$set": {"status": "completed"}}
    )
    return jsonify({"msg": "Booking marked as completed."}), 200

@bookings_bp.route('/<booking_id>/no-show', methods=['POST'])
@jwt_required()
def no_show_booking(booking_id):
    """ VENDOR-ONLY. Marks a confirmed booking as a no-show. """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    booking = db.bookings.find_one({"_id": ObjectId(booking_id), "seller_id": current_user_id})
    if not booking:
        return jsonify({"msg": "Booking not found or you are not the seller."}), 404

    db.bookings.update_one(
        {"_id": ObjectId(booking_id)},
        {"$set": {"status": "no_show"}}
    )
    return jsonify({"msg": "Booking marked as a no-show."}), 200




# Add this new route to the end of your bookings.py file

@bookings_bp.route('/daily-briefing', methods=['POST'])
@jwt_required()
def get_daily_briefing():
    """
    Receives a list of today's bookings and uses an AI agent
    to generate a natural language summary of the schedule.
    """
    from app import openai_client # Import the client from the app context
    data = request.get_json()
    
    bookings_for_today = data.get('bookings', [])
    
    # Format the data for the AI prompt
    schedule_summary = []
    for booking in bookings_for_today:
        client_name = booking.get('client_details', {}).get('username', 'Unknown Client')
        service_name = booking.get('service_details', {}).get('product_name', 'Appointment')
        start_time_str = booking.get('start_time', {}).get('$date')
        
        if start_time_str:
            start_time = datetime.datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            formatted_time = start_time.strftime('%I:%M %p') # e.g., 09:00 AM
            schedule_summary.append(
                f"- {service_name} with {client_name} at {formatted_time}"
            )

    schedule_as_text = "\n".join(schedule_summary)
    
    prompt = f"""
    You are a friendly and professional personal assistant for a business owner in Accra, Ghana.
    The current time is {datetime.datetime.now().strftime('%I:%M %p')}.
    Based on the following list of today's appointments, provide a concise, one or two-sentence summary of the day's schedule.

    - If there are no appointments, say something encouraging like: "Your schedule is clear today! A great opportunity to prepare for the week."
    - If there is one appointment, mention it directly. Example: "You have one appointment today: Haircut with Ama at 11:00 AM."
    - If there are multiple appointments, summarize them by mentioning the total number and the first one. Example: "You have 5 appointments today, starting with Kofi at 9:00 AM."
    - Keep the tone encouraging and professional. Do not just list the data. Be brief.

    Today's appointments:
    {schedule_as_text}
    """

    try:
        completion = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=100,
            temperature=0.7,
        )
        
        briefing = completion.choices[0].message.content
        return jsonify({"briefing": briefing}), 200

    except Exception as e:
        print(f"AI Daily Briefing Failed: {e}")
        return jsonify({"msg": "Failed to generate AI briefing.", "error": str(e)}), 500