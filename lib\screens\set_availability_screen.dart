import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/booking_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class SetAvailabilityScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const SetAvailabilityScreen({super.key, required this.businessData});

  @override
  State<SetAvailabilityScreen> createState() => _SetAvailabilityScreenState();
}

class _SetAvailabilityScreenState extends State<SetAvailabilityScreen> {
  final BookingService _bookingService = BookingService();
  final Map<String, Map<String, TimeOfDay?>> _schedule = {
    'monday': {'start': null, 'end': null},
    'tuesday': {'start': null, 'end': null},
    'wednesday': {'start': null, 'end': null},
    'thursday': {'start': null, 'end': null},
    'friday': {'start': null, 'end': null},
    'saturday': {'start': null, 'end': null},
    'sunday': {'start': null, 'end': null},
  };

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final existingSchedule =
        widget.businessData['availability'] as Map<String, dynamic>?;
    if (existingSchedule != null) {
      _schedule.forEach((day, times) {
        if (existingSchedule.containsKey(day)) {
          final dayData = existingSchedule[day] as Map<String, dynamic>;
          final startTimeStr = dayData['start'] as String?;
          final endTimeStr = dayData['end'] as String?;
          if (startTimeStr != null) {
            final startParts = startTimeStr.split(':');
            times['start'] = TimeOfDay(
              hour: int.parse(startParts[0]),
              minute: int.parse(startParts[1]),
            );
          }
          if (endTimeStr != null) {
            final endParts = endTimeStr.split(':');
            times['end'] = TimeOfDay(
              hour: int.parse(endParts[0]),
              minute: int.parse(endParts[1]),
            );
          }
        }
      });
    }
  }

  Future<void> _selectTime(
    BuildContext context,
    String day,
    String type,
  ) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _schedule[day]![type] ?? const TimeOfDay(hour: 9, minute: 0),
    );
    if (picked != null) {
      setState(() {
        _schedule[day]![type] = picked;
      });
    }
  }

  Future<void> _saveSchedule() async {
    setState(() => _isLoading = true);

    final scheduleToSave = <String, dynamic>{};
    _schedule.forEach((day, times) {
      if (times['start'] != null && times['end'] != null) {
        scheduleToSave[day] = {
          'start':
              '${times['start']!.hour.toString().padLeft(2, '0')}:${times['start']!.minute.toString().padLeft(2, '0')}',
          'end':
              '${times['end']!.hour.toString().padLeft(2, '0')}:${times['end']!.minute.toString().padLeft(2, '0')}',
        };
      }
    });

    try {
      await _bookingService.setAvailability(
        businessId: widget.businessData['_id']['\$oid'],
        schedule: scheduleToSave,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Schedule saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            'Set Schedule',
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      // --- REFACTORED: Use a Stack for the layout ---
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.fromLTRB(
              16,
              16,
              16,
              100,
            ), // Padding at bottom for button
            children: _schedule.keys.map((day) {
              return _buildDayRow(day);
            }).toList(),
          ),
          Positioned(
            left: 16,
            right: 16,
            bottom: 16,
            child: GestureDetector(
              onTap: _isLoading ? null : _saveSchedule,
              child: NeuCard(
                margin: EdgeInsets.zero,
                backgroundColor: const Color(0xFF00D2D3),
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          height: 24,
                          width: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 3,
                          ),
                        )
                      : const Text(
                          'Save Schedule',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayRow(String day) {
    final start = _schedule[day]!['start'];
    final end = _schedule[day]!['end'];
    final bool isActive = start != null && end != null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: NeuCard(
        margin: EdgeInsets.zero,
        backgroundColor: isActive ? Colors.white : Colors.grey.shade200,
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                '${day[0].toUpperCase()}${day.substring(1)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isActive ? Colors.black : Colors.grey.shade600,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _selectTime(context, day, 'start'),
                      child: NeuCard(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.symmetric(
                          vertical: 10,
                          horizontal: 8,
                        ),
                        child: Text(
                          start?.format(context) ?? 'Start',
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _selectTime(context, day, 'end'),
                      child: NeuCard(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.symmetric(
                          vertical: 10,
                          horizontal: 8,
                        ),
                        child: Text(
                          end?.format(context) ?? 'End',
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
