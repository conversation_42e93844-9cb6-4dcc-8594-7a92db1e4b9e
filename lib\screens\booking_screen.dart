import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:wicker/services/booking_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class BookingScreen extends StatefulWidget {
  final Map<String, dynamic> productData;
  const BookingScreen({super.key, required this.productData});

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  final BookingService _bookingService = BookingService();

  // State
  Future<List<String>>? _slotsFuture;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  String? _selectedSlot;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _fetchSlotsForDay(_selectedDay!);
  }

  void _fetchSlotsForDay(DateTime day) {
    setState(() {
      _slotsFuture = _bookingService.getAvailability(
        businessId: widget.productData['business_id']['\$oid'],
        serviceId: widget.productData['_id']['\$oid'], // <-- ADD THIS
        date: day,
      );
    });
  }

  Future<void> _handleBookingRequest() async {
    if (_selectedDay == null || _selectedSlot == null) return;

    setState(() => _isLoading = true);

    try {
      final timeParts = _selectedSlot!.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      final startTime = DateTime(
        _selectedDay!.year,
        _selectedDay!.month,
        _selectedDay!.day,
        hour,
        minute,
      );

      await _bookingService.requestBooking(
        businessId: widget.productData['business_id']['\$oid'],
        serviceId: widget.productData['_id']['\$oid'],
        startTime: startTime,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking request sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: AppBar(
        title: Text('Book: ${widget.productData['product_name'] ?? 'Service'}'),
      ),
      // --- REFACTORED: Use a Stack for the layout ---
      body: Stack(
        children: [
          // The main scrollable content
          SingleChildScrollView(
            // Add padding at the bottom to ensure content isn't hidden by the button
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
            child: Column(
              children: [
                _buildCalendar(),
                const SizedBox(height: 16),
                _buildTimeSlots(),
              ],
            ),
          ),
          // The floating "Confirm" button at the bottom
          Positioned(
            left: 16,
            right: 16,
            bottom: 16,
            child: GestureDetector(
              onTap: (_selectedSlot == null || _isLoading)
                  ? null
                  : _handleBookingRequest,
              child: NeuCard(
                margin: EdgeInsets.zero,
                backgroundColor: _selectedSlot == null
                    ? Colors.grey
                    : const Color(0xFF00D2D3),
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          height: 24,
                          width: 24,
                          child: CircularProgressIndicator(color: Colors.white),
                        )
                      : const Text(
                          'Confirm Booking Request',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendar() {
    return NeuCard(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(8),
      child: TableCalendar(
        firstDay: DateTime.now(),
        lastDay: DateTime.now().add(const Duration(days: 365)),
        focusedDay: _focusedDay,
        selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
        onDaySelected: (selectedDay, focusedDay) {
          setState(() {
            _selectedDay = selectedDay;
            _focusedDay = focusedDay;
            _selectedSlot = null; // Reset selected slot when day changes
          });
          _fetchSlotsForDay(selectedDay);
        },
        calendarStyle: const CalendarStyle(
          todayDecoration: BoxDecoration(
            color: Color(0xFF4ECDC4),
            shape: BoxShape.circle,
          ),
          selectedDecoration: BoxDecoration(
            color: Color(0xFF6C5CE7),
            shape: BoxShape.circle,
          ),
        ),
        headerStyle: const HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
        ),
      ),
    );
  }

  Widget _buildTimeSlots() {
    return FutureBuilder<List<String>>(
      future: _slotsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Padding(
            padding: EdgeInsets.all(32.0),
            child: Center(child: CircularProgressIndicator()),
          );
        }
        if (snapshot.hasError) {
          return NeuCard(
            child: Center(child: Text('Error: ${snapshot.error}')),
          );
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return NeuCard(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No available slots for ${DateFormat.yMMMd().format(_selectedDay!)}.',
                ),
              ),
            ),
          );
        }

        final slots = snapshot.data!;
        return Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children: slots.map((slot) {
            final isSelected = _selectedSlot == slot;
            return GestureDetector(
              onTap: () => setState(() => _selectedSlot = slot),
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                backgroundColor: isSelected
                    ? const Color(0xFF6C5CE7)
                    : Colors.white,
                child: Text(
                  slot,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }
}
